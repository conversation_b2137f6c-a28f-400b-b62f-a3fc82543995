# 学生权限控制系统设计文档

## 1. 概述

本文档描述了学生查看权限控制系统的设计，包括权限枚举、数据表结构、权限控制逻辑以及表关联关系。

## 2. 权限枚举定义

### 2.1 SystemMenuPermissionEnum

```java
// 学生相关权限
STUDENT_VIEW("STUDENT_VIEW", "查看权限", "Student View Authority"),
STUDENT_OPERATE("STUDENT_OPERATE", "操作权限", "Student Operate Authority"),

// 学生查看范围权限
STUDENT_VIEW_PERSON("STUDENT_VIEW_PERSON", "个人", "Student View Person Authority"),
STUDENT_VIEW_ALL("STUDENT_VIEW_ALL", "全部", "Student View All Authority"),
```

### 2.2 权限控制逻辑

1. **STUDENT_VIEW_ALL（全部权限）**：
   - 能够查看代理下的所有学生
   - 不受上下级关系影响
   - 设置 `roleTypeFlag=false`

2. **STUDENT_VIEW_PERSON（个人权限）**：
   - 只能看到自己创建或分派的学生
   - 如果有下级，可以看到下级能看到的学生
   - 设置 `roleTypeFlag=true`

## 3. 数据表结构

### 3.1 核心业务表

#### 3.1.1 m_student（学生基本信息表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 学生ID（主键） |
| name | VARCHAR | 学生姓名 |
| first_name | VARCHAR | 名 |
| last_name | VARCHAR | 姓 |
| mobile | VARCHAR | 手机号 |
| email | VARCHAR | 邮箱 |
| fk_agent_id | BIGINT | 代理商ID |
| ... | ... | 其他学生信息字段 |

#### 3.1.2 m_student_offer_item（学生申请项目表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 申请项目ID（主键） |
| fk_student_id | BIGINT | 学生ID（外键） |
| fk_agent_id | BIGINT | 代理商ID |
| fk_institution_id | BIGINT | 院校ID |
| fk_institution_course_id | BIGINT | 课程ID |
| fk_area_country_id | BIGINT | 国家ID |
| fk_student_offer_item_step_id | BIGINT | 申请步骤ID |
| status | INT | 状态（1=有效） |
| is_follow | TINYINT | 是否跟进（0=否） |
| is_follow_hidden | TINYINT | 是否隐藏跟进（0=否） |

#### 3.1.3 m_app_student（应用学生信息表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 应用学生ID（主键） |
| fk_student_id | BIGINT | 学生ID（外键） |
| fk_platform_create_user_id | BIGINT | 平台创建用户ID |
| fk_platform_code | VARCHAR | 平台代码（PARTNER） |
| status | INT | 状态（2=已审核通过） |
| fk_agent_id | BIGINT | 代理商ID |
| fk_company_id | BIGINT | 公司ID |

### 3.2 权限控制表

#### 3.2.1 m_partner_user（合作伙伴用户表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 合作伙伴用户ID（主键） |
| fk_user_id | BIGINT | 系统用户ID |
| fk_agent_id | BIGINT | 代理商ID |
| fk_company_id | BIGINT | 公司ID |
| name | VARCHAR | 姓名（中文） |
| name_en | VARCHAR | 姓名（英文） |
| is_active | BOOLEAN | 是否激活 |
| is_admin | INT | 是否超级管理员（0=否，1=是） |

#### 3.2.2 r_partner_user_student（用户学生关系表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 关系ID（主键） |
| fk_partner_user_id | BIGINT | 合作伙伴用户ID |
| fk_student_id | BIGINT | 学生ID |
| is_active | BOOLEAN | 是否激活（1=是） |
| active_date | DATETIME | 绑定时间 |
| unactive_date | DATETIME | 取消绑定时间 |

#### 3.2.3 r_partner_user_superior（用户上下级关系表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 关系ID（主键） |
| fk_partner_user_id | BIGINT | 下级用户ID |
| fk_partner_user_id_superior | BIGINT | 上级用户ID |
| fk_tenant_id | BIGINT | 租户ID |

### 3.3 字典表

#### 3.3.1 u_student_offer_item_step（申请步骤字典表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 步骤ID（主键） |
| step_name | VARCHAR | 步骤名称 |
| step_order | INT | 步骤顺序 |

#### 3.3.2 m_institution（院校信息表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 院校ID（主键） |
| name | VARCHAR | 院校名称（英文） |
| name_chn | VARCHAR | 院校名称（中文） |

#### 3.3.3 m_institution_course（院校课程表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 课程ID（主键） |
| name | VARCHAR | 课程名称 |

#### 3.3.4 u_area_country（国家字典表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 国家ID（主键） |
| name | VARCHAR | 国家名称（英文） |
| name_chn | VARCHAR | 国家名称（中文） |

### 3.4 UUID关系表

#### 3.4.1 r_student_uuid（学生UUID关系表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 关系ID（主键） |
| fk_student_id | BIGINT | 学生ID |
| fk_student_uuid | VARCHAR | 学生UUID |

## 4. 权限控制SQL设计

### 4.1 原有权限控制SQL（offerItemPermissionRoleSql）
```xml
<sql id="offerItemPermissionRoleSql">
    <if test="roleTypeFlag==true">
        INNER JOIN (
            SELECT DISTINCT fk_student_id FROM r_partner_user_student
            WHERE is_active=1 AND fk_partner_user_id = #{partnerUserId}
            UNION
            SELECT DISTINCT fk_student_id FROM m_app_student
            WHERE fk_platform_create_user_id = #{partnerUserId} 
              AND fk_platform_code='PARTNER' AND status=2
        ) distributionStudent ON offerItem.fk_student_id = distributionStudent.fk_student_id
    </if>
</sql>
```

### 4.2 新增支持下级权限的SQL（offerItemPermissionRoleSqlWithSubordinate）
```xml
<sql id="offerItemPermissionRoleSqlWithSubordinate">
    <if test="roleTypeFlag==true">
        INNER JOIN (
            SELECT DISTINCT fk_student_id FROM r_partner_user_student
            WHERE is_active=1 AND fk_partner_user_id IN
            <foreach collection="levelPartnerUserIds" item="partUserId" open="(" separator="," close=")">
               #{partUserId}
            </foreach>
            UNION
            SELECT DISTINCT fk_student_id FROM m_app_student
            WHERE fk_platform_create_user_id IN
            <foreach collection="levelPartnerUserIds" item="partUserId" open="(" separator="," close=")">
               #{partUserId}
            </foreach>
            AND fk_platform_code='PARTNER' AND status=2
        ) distributionStudent ON offerItem.fk_student_id = distributionStudent.fk_student_id
    </if>
</sql>
```

## 5. 表关联关系图

```mermaid
erDiagram
    %% 核心业务表
    m_student["m_student(学生表)"] {
        bigint id PK "学生ID"
        varchar name "学生姓名"
        varchar first_name "名"
        varchar last_name "姓"
        varchar mobile "手机号"
        varchar email "邮箱"
        bigint fk_agent_id FK "代理商ID"
    }

    m_student_offer_item["m_student_offer_item(学生申请项目表)"] {
        bigint id PK "申请项目ID"
        bigint fk_student_id FK "学生ID"
        bigint fk_agent_id FK "代理商ID"
        bigint fk_institution_id FK "院校ID"
        bigint fk_institution_course_id FK "课程ID"
        bigint fk_area_country_id FK "国家ID"
        bigint fk_student_offer_item_step_id FK "申请步骤ID"
        int status "状态(1=有效)"
        tinyint is_follow "是否跟进(0=否)"
        tinyint is_follow_hidden "是否隐藏跟进(0=否)"
    }

    m_app_student["m_app_student(应用学生表)"] {
        bigint id PK "应用学生ID"
        bigint fk_student_id FK "学生ID"
        bigint fk_platform_create_user_id FK "平台创建用户ID"
        varchar fk_platform_code "平台代码(PARTNER)"
        int status "状态(2=已审核通过)"
        bigint fk_agent_id FK "代理商ID"
        bigint fk_company_id FK "公司ID"
    }

    %% 权限控制表
    m_partner_user["m_partner_user(合作伙伴用户表)"] {
        bigint id PK "合作伙伴用户ID"
        bigint fk_user_id FK "系统用户ID"
        bigint fk_agent_id FK "代理商ID"
        bigint fk_company_id FK "公司ID"
        varchar name "姓名(中文)"
        varchar name_en "姓名(英文)"
        boolean is_active "是否激活"
        int is_admin "是否超级管理员"
    }

    r_partner_user_student["r_partner_user_student(用户学生关系表)"] {
        bigint id PK "关系ID"
        bigint fk_partner_user_id FK "合作伙伴用户ID"
        bigint fk_student_id FK "学生ID"
        boolean is_active "是否激活(1=是)"
        datetime active_date "绑定时间"
        datetime unactive_date "取消绑定时间"
    }

    r_partner_user_superior["r_partner_user_superior(用户上下级关系表)"] {
        bigint id PK "关系ID"
        bigint fk_partner_user_id FK "下级用户ID"
        bigint fk_partner_user_id_superior FK "上级用户ID"
        bigint fk_tenant_id FK "租户ID"
    }

    %% UUID关系表
    r_student_uuid["r_student_uuid(学生UUID关系表)"] {
        bigint id PK "关系ID"
        bigint fk_student_id FK "学生ID"
        varchar fk_student_uuid "学生UUID"
    }

    %% 字典表
    u_student_offer_item_step["u_student_offer_item_step(申请步骤字典表)"] {
        bigint id PK "步骤ID"
        varchar step_name "步骤名称"
        int step_order "步骤顺序"
    }

    m_institution["m_institution(院校信息表)"] {
        bigint id PK "院校ID"
        varchar name "院校名称(英文)"
        varchar name_chn "院校名称(中文)"
    }

    m_institution_course["m_institution_course(院校课程表)"] {
        bigint id PK "课程ID"
        varchar name "课程名称"
    }

    u_area_country["u_area_country(国家字典表)"] {
        bigint id PK "国家ID"
        varchar name "国家名称(英文)"
        varchar name_chn "国家名称(中文)"
    }

    %% 关联关系
    %% 核心业务关联
    m_student ||--o{ m_student_offer_item : "一个学生有多个申请项目"
    m_student ||--o| m_app_student : "一个学生对应一个应用学生记录"
    m_student ||--|| r_student_uuid : "一个学生对应一个UUID"

    %% 申请项目关联
    m_student_offer_item }o--|| m_institution : "申请项目关联院校"
    m_student_offer_item }o--|| m_institution_course : "申请项目关联课程"
    m_student_offer_item }o--|| u_area_country : "申请项目关联国家"
    m_student_offer_item }o--|| u_student_offer_item_step : "申请项目关联步骤"

    %% 权限控制关联
    m_partner_user ||--o{ r_partner_user_student : "用户可以分配多个学生"
    m_student ||--o{ r_partner_user_student : "学生可以分配给多个用户"

    %% 上下级关系
    m_partner_user ||--o{ r_partner_user_superior : "用户作为下级"
    m_partner_user ||--o{ r_partner_user_superior : "用户作为上级"

    %% 创建关系
    m_partner_user ||--o{ m_app_student : "用户创建应用学生记录"
```

## 6. 数据库分布

| 数据库 | 表名 | 用途 |
|--------|------|------|
| `ais_sale_center` | m_student, m_student_offer_item, m_app_student, r_student_uuid, u_student_offer_item_step | 销售中心核心业务数据 |
| `ais_institution_center` | m_institution, m_institution_course, u_area_country | 院校中心数据 |
| `app_partner_center` | m_partner_user, r_partner_user_student, r_partner_user_superior | 合作伙伴中心数据 |

## 7. 权限控制流程

1. **用户登录** → 获取用户权限信息
2. **权限判断** → 检查是否有 STUDENT_VIEW_ALL 或 STUDENT_VIEW_PERSON 权限
3. **参数设置** → 根据权限设置 roleTypeFlag 和 levelPartnerUserIds
4. **SQL执行** → 使用对应的权限控制SQL片段
5. **数据返回** → 返回用户有权限查看的学生列表

## 8. 权限控制流程图

```mermaid
flowchart TD
    A[用户访问getPeopleStudentList] --> B[获取用户权限信息<br/>📋 system_menu权限表]
    B --> C{检查权限<br/>📋 m_partner_role_menu角色菜单表}
    C -->|无权限| D[返回空结果]
    C -->|有STUDENT_VIEW_ALL| E[设置roleTypeFlag=false<br/>🔓 查看代理下所有学生]
    C -->|有STUDENT_VIEW_PERSON| F[设置roleTypeFlag=true<br/>🔒 个人权限模式]

    E --> G[查询代理下所有学生<br/>📋 m_student_offer_item申请项目表<br/>📋 m_student学生表]
    F --> H[获取levelPartnerUserIds<br/>包含自己+下级用户ID<br/>📋 r_partner_user_superior上下级关系表]

    H --> I[使用offerItemPermissionRoleSqlWithSubordinate<br/>🔍 权限过滤SQL]
    I --> J[查询分配权限学生<br/>📋 r_partner_user_student用户学生关系表<br/>条件: is_active=1]
    I --> K[查询创建权限学生<br/>📋 m_app_student应用学生表<br/>条件: status=2已审核通过]

    J --> L[UNION合并结果<br/>🔗 合并分配+创建权限]
    K --> L
    G --> M[返回学生列表<br/>📋 最终结果包含:<br/>- m_student学生信息<br/>- m_institution院校信息<br/>- u_area_country国家信息<br/>- u_student_offer_item_step步骤信息]
    L --> M
    D --> N[结束]
    M --> N

    %% 样式定义
    classDef tableStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef permissionStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef resultStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class B,C,H,J,K tableStyle
    class E,F,I permissionStyle
    class G,L,M resultStyle
```

## 9. 数据权限控制说明

### 9.1 分配权限（r_partner_user_student表）
- **用途**：记录哪些学生被分配给哪些用户管理
- **权限控制**：用户只能看到分配给自己或下级的学生
- **条件**：`is_active=1` 且 `fk_partner_user_id IN (levelPartnerUserIds)`

### 9.2 创建权限（m_app_student表）
- **用途**：记录哪些学生申请是由哪些用户创建的
- **权限控制**：用户只能看到自己或下级创建的学生申请
- **条件**：`fk_platform_create_user_id IN (levelPartnerUserIds)` 且 `status=2`（已审核通过）

### 9.3 上下级关系（r_partner_user_superior表）
- **用途**：维护用户的上下级关系
- **权限扩展**：个人权限用户可以看到下级用户的学生
- **实现**：通过递归查询获取所有下级用户ID，组成`levelPartnerUserIds`

## 10. 修改说明

### 10.1 保持原有代码不变
- `offerItemPermissionRoleSql` 保持原有逻辑不变
- 其他使用该SQL的功能不受影响

### 10.2 新增功能
- 新增 `offerItemPermissionRoleSqlWithSubordinate` 支持下级权限
- `getPeopleStudentList` 方法使用新的权限控制逻辑

### 10.3 实现效果
- ✅ 全部权限用户：查看代理下所有学生
- ✅ 个人权限用户：查看自己+下级用户的学生

## 11. 技术要点

### 11.1 MyBatis动态SQL
- 使用`<if test="roleTypeFlag==true">`控制权限分支
- 使用`<foreach>`遍历`levelPartnerUserIds`集合
- 使用`UNION`合并分配权限和创建权限的结果

### 11.2 权限数据获取
- `UserInfoParamsUtils.getUserInfoParams()`获取用户基础信息
- `RPartnerUserSuperiorService.getAllSubUserIds()`获取下级用户ID
- `levelPartnerUserIds`包含当前用户+所有下级用户ID

### 11.3 缓存机制
- 用户权限信息缓存在Redis中，提高查询性能
- 上下级关系变更时自动清除相关缓存
