<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzh.job.mapper.LogLivePartnerUserAppointmentSenderMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.LogLivePartnerUserAppointmentSenderEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkLiveId" column="fk_live_id" jdbcType="BIGINT"/>
            <result property="fkPartnerUserId" column="fk_partner_user_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fk_live_id,fk_partner_user_id,
        status,remark,gmt_create,
        gmt_create_user,gmt_modified,gmt_modified_user
    </sql>
</mapper>
