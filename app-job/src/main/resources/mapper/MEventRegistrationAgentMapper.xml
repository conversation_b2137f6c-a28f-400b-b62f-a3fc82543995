<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzh.job.mapper.MEventRegistrationAgentMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MEventRegistrationAgentEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkEventId" column="fk_event_id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkPartnerUserId" column="fk_partner_user_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="mobileAreaCode" column="mobile_area_code" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="peopleCount" column="people_count" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>
        <select id="selectDetail" resultType="com.partner.entity.EventEntity">
            SELECT DISTINCT mEvent.id FROM ais_sale_center.m_event mEvent
                INNER JOIN ais_sale_center.m_event_registration_agent mEventRegistrationAgent  ON mEvent.id=mEventRegistrationAgent.fk_event_id
                LEFT JOIN  app_partner_center.r_event_partner_user_appointment rEventPartnerUserAppointment ON rEventPartnerUserAppointment.fk_event_registration_agent_id=mEventRegistrationAgent.id
            WHERE mEvent.event_time  BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 1 DAY )
                    AND mEventRegistrationAgent.status=1   AND rEventPartnerUserAppointment.status is NULL
            LIMIT   10;

        </select>
        <select id="selectDetailList" resultType="com.partner.vo.job.UserMEventVo">


                SELECT mEventRegistrationAgent.*,
                       mEvent.event_theme AS title,
                       mEvent.event_time,
                       uAreaCountry.name_chn               AS areaCountryName,
                       uAreaState.name_chn               AS areaStateName,
                       uAreaCity.name_chn            AS areaCityName,
                       systemUserPlatformLogin.mini_program_openid AS touser FROM
                        ais_sale_center.m_event_registration_agent mEventRegistrationAgent
                                INNER JOIN ais_sale_center.m_event mEvent ON mEvent.id=mEventRegistrationAgent.fk_event_id
                                INNER JOIN app_partner_center.m_partner_user mPartnerUser ON mEventRegistrationAgent.fk_partner_user_id=mPartnerUser.id
                                INNER JOIN app_system_center.system_user_platform_login systemUserPlatformLogin
                                           ON mPartnerUser.fk_user_id =systemUserPlatformLogin.fk_user_id
                                LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON uAreaCountry.id = mEvent.fk_area_country_id_hold
                                LEFT JOIN ais_institution_center.u_area_state uAreaState ON uAreaState.id = mEvent.fk_area_state_id_hold
                                LEFT JOIN ais_institution_center.u_area_city uAreaCity ON uAreaCity.id = mEvent.fk_area_city_id_hold
                                LEFT JOIN  app_partner_center.r_event_partner_user_appointment rEventPartnerUserAppointment ON rEventPartnerUserAppointment.fk_event_registration_agent_id=mEventRegistrationAgent.id
                WHERE  mEventRegistrationAgent.fk_event_id=#{meventId}
                        AND mEventRegistrationAgent.status=1
                        AND rEventPartnerUserAppointment.status is NULL


        </select>

</mapper>
