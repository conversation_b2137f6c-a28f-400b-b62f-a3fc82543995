<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzh.job.mapper.LogEventPartnerUserAppointmentSenderMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.LogEventPartnerUserAppointmentSenderEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkEventRegistrationAgentId" column="fk_event_registration_agent_id" jdbcType="BIGINT"/>
            <result property="fkPartnerUserId" column="fk_partner_user_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>
