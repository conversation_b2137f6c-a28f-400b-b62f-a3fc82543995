server:
  port: 7019

spring:
  application:
    name: app-job
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:************}:${NACOS_PORT:8852}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:<EMAIL>@.yml
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true

#mybatis-plus配置控制台打印完整带参数SQL语句
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


#配置日志
logging:
  config: classpath:log/<EMAIL>@.xml

