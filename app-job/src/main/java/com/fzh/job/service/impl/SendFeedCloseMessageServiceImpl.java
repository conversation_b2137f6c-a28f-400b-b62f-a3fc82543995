package com.fzh.job.service.impl;

import com.fzh.job.dto.BaseParamDto;
import com.fzh.job.mqconfig.SendFeedClose;
import com.fzh.job.service.SendBaseMessageService;
import com.partner.mqmessage.FeedCloseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;


@Service("FEEDCLOSEMESSAGE")
@Slf4j
public class SendFeedCloseMessageServiceImpl implements SendBaseMessageService {

    @Resource
    SendFeedClose sendFeedClose;

    @Override
    public Boolean sendMQMessage(BaseParamDto params) {
        //反馈 超期关闭定时任务
        log.info("==============SendFeedCloseMessageServiceImpl=========");
        FeedCloseMessage messageParams=new FeedCloseMessage();
        messageParams.setAgentId(params.getAgentId());

        sendFeedClose.sendCommissionMessage(messageParams);


        return true;
    }
}
