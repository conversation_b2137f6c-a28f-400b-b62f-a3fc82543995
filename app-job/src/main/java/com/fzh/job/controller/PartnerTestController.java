package com.fzh.job.controller;


import com.common.core.util.R;
import com.fzh.job.dto.BaseParamDto;
import com.fzh.job.mqconfig.SendFactory;
import com.fzh.job.service.MLiveMessageSendService;
import com.fzh.job.service.MStudentOfferItemAgentConfirmService;
import com.fzh.job.service.SendConfimCommissionService;
import com.partner.mqmessage.MQCommissionMessage;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/partnerTest")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class PartnerTestController {
    private final MStudentOfferItemAgentConfirmService mStudentOfferItemAgentConfirmService;

    private final MLiveMessageSendService mliveMessageSendService;

    private final SendConfimCommissionService sendConfimCommissionService;


    private final SendFactory sendFactory;

    @PostMapping("/commissionAffirm" )
    public R getCommissionAffirmPage() {
        BaseParamDto params=new BaseParamDto();
        mStudentOfferItemAgentConfirmService.offerConfirm(params);
        return R.ok();

    }


    @PostMapping("/sendMliveMessage" )
    public R MLiveMessageSendService() {
        BaseParamDto params=new BaseParamDto();
        mliveMessageSendService.sendLiveMessage(params);
        return R.ok();

    }
    @PostMapping("/sendMQMessage" )
    public R sendMQMessage() {
        BaseParamDto params=new BaseParamDto();
        sendConfimCommissionService.sendMQCommissionMessage(params);
        return R.ok();

    }

    @PostMapping("/sendConfirm" )
    public R sendConfirm() {
        MQCommissionMessage messageParams=new MQCommissionMessage();
        messageParams.setAgentId(66666l);
        messageParams.setStudentId(10l);
        sendConfimCommissionService.sendCommissionMessage(messageParams);
        return R.ok();

    }


    @PostMapping("/sendFactory" )
    public R sendFactory() {
        BaseParamDto params=new BaseParamDto();
        Boolean flag=sendFactory.getMessageObject("FEEDCLOSEMESSAGE").sendMQMessage(params);

        return R.ok();

    }


}
