server:
  port: 6014

spring:
  application:
    name: app-insurance-biz
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: 192.168.2.31:${NACOS_PORT:8852}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml

#配置日志
logging:
  config: classpath:log/<EMAIL>@.xml

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

rocketmq:
  name-server: 192.168.2.28:9876
  producer:
    group: insurance_producer_group


