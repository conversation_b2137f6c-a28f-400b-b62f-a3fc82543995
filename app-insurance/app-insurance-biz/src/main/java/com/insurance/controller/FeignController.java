
package com.insurance.controller;


import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.insurance.event.publisher.SendNotifyMessageEventPublisher;
import com.insurance.service.CreditCardService;
import com.insurance.service.InsuranceOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@Slf4j
@Inner(false)
@RequestMapping("/feign")
@Tag(description = "远程管理", name = "远程管理")
public class FeignController {

    @Autowired
    private InsuranceOrderService orderService;
    @Autowired
    private CreditCardService creditCardService;
    @Autowired
    private SendNotifyMessageEventPublisher eventPublisher;

    @Operation(summary = "发送下单请求")
    @Inner(false)
    @GetMapping("/order/sendRequest")
    public R sendRequest() {
        log.info("发送下单请求");
        orderService.sendRequest(null);
        return R.ok();
    }

    @Operation(summary = "还款提醒")
    @Inner(false)
    @GetMapping("/creditCard/sendRepaymentNotify")
    public R sendRepaymentNotify() {
        log.info("发送信用卡还款提醒==================>");
        creditCardService.sendRepaymentNotify(2);
        return R.ok();
    }

    @Operation(summary = "出账提醒")
    @Inner(false)
    @GetMapping("/creditCard/sendStatementNotify")
    public R sendStatementNotify() {
        log.info("发送信用卡出账提醒==================>");
        creditCardService.sendRepaymentNotify(1);
        return R.ok();
    }

    @Operation(summary = "额度提醒")
    @Inner(false)
    @GetMapping("/creditCard/sendQuotaRemindNotify")
    public R sendQuotaRemindNotify() {
        log.info("发送信用卡额度提醒==================>");
        creditCardService.sendQuotaRemindNotify();
        return R.ok();
    }

    @Operation(summary = "消息通知测试")
    @Inner(false)
    @GetMapping("/notifyTest")
    public R notifyTest(String orderNo, String code, Long creditCardId) {
        log.info("消息通知测试==========》");
        eventPublisher.publishPartnerUserOfflineEvent(creditCardId, code, orderNo, new Date());
        return R.ok();
    }
}
