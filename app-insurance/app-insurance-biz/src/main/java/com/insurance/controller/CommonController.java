
package com.insurance.controller;


import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.insurance.mapper.InstitutionCenterMapper;
import com.insurance.util.EncryptionUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/common")
@Inner(false)
@Tag(description = "通用管理", name = "通用管理")
public class CommonController {

    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private EncryptionUtils encryptionUtils;

    @Operation(summary = "国家列表")
    @GetMapping("/countryList")
    public R countryList() {
        return R.ok(institutionCenterMapper.selectCountryList());
    }

    @Operation(summary = "州省列表")
    @GetMapping("/areaStateList")
    public R areaStateList(@Parameter(description = "国家ID") Long countryId) {
        return R.ok(institutionCenterMapper.selectAreaState(countryId));
    }

    @Operation(summary = "获取密钥")
    @GetMapping("/getSecret")
    @Inner(false)
    public R areaStateList() {
        return R.ok(encryptionUtils.getSecret());
    }

}
