package com.insurance.rocketmq.msg;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author:<PERSON>
 * @Date: 2025/4/29
 * @Version 1.0
 * @apiNote:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderMsg {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "保单号")
    private String insuranceNo;

    @Schema(description = "价格")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "0.00")
    private BigDecimal price;

    @Schema(description = "下单错误信息")
    private String errorMsg;
}
