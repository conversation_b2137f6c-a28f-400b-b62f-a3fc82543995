package com.insurance.rocketmq.msg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/5/30
 * @Version 1.0
 * @apiNote:下单成功发送创建应收应付消息体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InsurancePlanMsg {

    @Schema(description = "应收类型关键字，枚举，如：m_student_offer_item")
    private String fkTypeKey;

    @Schema(description = "应收类型对应记录Id，如：m_student_offer_item.id")
    private Long fkTypeTargetId;

    @Schema(description = "币种编号")
    private String fkCurrencyTypeNum;

    @Schema(description = "保险金额")
    private BigDecimal tuitionAmount;

    @Schema(description = "应收费率%")
    private BigDecimal receivablePlanCommissionRate;

    @Schema(description = "应付费率%")
    private BigDecimal payablePlanCommissionRate;

}
