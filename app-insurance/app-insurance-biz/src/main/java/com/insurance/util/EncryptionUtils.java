package com.insurance.util;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.insurance.config.EncryptionConfig;
import com.insurance.vo.encryption.EncryptionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author:Oliver
 * @Date: 2025/7/31
 * @Version 1.0
 * @apiNote:信用卡密钥工具类
 */
@Component
@Slf4j
public class EncryptionUtils {

    @Autowired
    private EncryptionConfig encryptionConfig;


    public String getSecret() {
        String resultStr = HttpUtil.get(encryptionConfig.getHost());
        log.info("获取密钥结果:{}", resultStr);
        EncryptionResult encryptionResult = JSONObject.parseObject(resultStr, EncryptionResult.class);
        if (!encryptionResult.getSuccess()) {
            log.error("获取密钥失败:{}", JSONObject.toJSONString(encryptionResult));
            throw new RuntimeException("获取密钥失败");
        }
        if (StringUtils.isBlank(encryptionResult.getKey())) {
            log.error("获取密钥失败:{}", JSONObject.toJSONString(encryptionResult));
            throw new RuntimeException("获取密钥失败");
        }
        return phaseKey(encryptionResult);
    }

    public String phaseKey(EncryptionResult encryptionResult) {
        return null;
    }

//    @PostConstruct
//    public void init() {
//        log.info("【EncryptionUtils 配置检测】初始化缓存并检测配置...");
//        this.secretCache = Caffeine.newBuilder()
//                .expireAfterWrite(10, TimeUnit.MINUTES)
//                .maximumSize(1)
//                .build();
//
//        if (encryptionConfig == null) {
//            log.error("EncryptionConfig 未注入！");
//        } else {
//            log.info("EncryptionConfig 配置如下：host: {}", encryptionConfig.getHost());
//            if (StringUtils.isEmpty(encryptionConfig.getSecret())) {
//                log.error("加密 secret 配置为空！");
//            } else {
//                // 可选：项目启动时预加载
//                try {
//                    getSecret(); // 触发一次缓存加载
//                } catch (Exception e) {
//                    log.warn("项目启动时预加载密钥失败，可忽略: {}", e.getMessage());
//                }
//            }
//        }
//    }
//
//    public String getSecret() {
//        try {
//            // 从缓存中获取，如果没有则自动拉取并缓存
//            return secretCache.get("secret", key -> {
//                log.info("缓存未命中，开始请求密钥...");
//                return fetchSecretFromRemote();
//            });
//        } catch (Exception e) {
//            log.error("获取密钥失败: {}", e.getMessage(), e);
//            throw new RuntimeException("获取密钥失败", e);
//        }
//    }
//
//    /**
//     * 请求远程服务获取密钥
//     */
//    private String fetchSecretFromRemote() {
//        log.info("请求远程获取密钥，host:{}", encryptionConfig.getHost());
//        String resultStr = HttpUtil.get(encryptionConfig.getHost());
//        log.info("获取密钥结果:{}", resultStr);
//
//        EncryptionResult encryptionResult = JSONObject.parseObject(resultStr, EncryptionResult.class);
//        if (!encryptionResult.getSuccess()) {
//            log.error("远程密钥获取失败: {}", resultStr);
//            throw new RuntimeException("远程密钥获取失败");
//        }
//        if (StringUtils.isBlank(encryptionResult.getKey())) {
//            log.error("密钥内容为空: {}", resultStr);
//            throw new RuntimeException("密钥内容为空");
//        }
//        return phaseKey(encryptionResult);
//    }
//
//    public String phaseKey(EncryptionResult encryptionResult) {
//        Key secretKey = EncoderUtil.changeKey(encryptionConfig.getSecret());
//        Claims claims = EncoderUtil.phaseTokenGetBody(encryptionResult.getKey(), secretKey);
//        Object keyObj = claims.get("key");
//        if (Objects.isNull(keyObj)) {
//            log.error("Token 中不包含 key 字段");
//            throw new IllegalArgumentException("Token 中不包含 key 字段");
//        }
//        return keyObj.toString();
//    }

}
