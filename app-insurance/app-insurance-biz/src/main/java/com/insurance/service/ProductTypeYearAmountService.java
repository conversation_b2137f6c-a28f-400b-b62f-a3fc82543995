package com.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.insurance.dto.order.InsurancePlanDTO;
import com.insurance.entity.ProductTypeYearAmount;

import java.math.BigDecimal;

/**
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote: 保险金额配置表 服务类接口
 */
public interface ProductTypeYearAmountService extends IService<ProductTypeYearAmount> {

    BigDecimal getDailyRate(InsurancePlanDTO insurancePlanDTO);

    BigDecimal calculateInsuranceEstimatedAmount(InsurancePlanDTO insurancePlanDTO);

}
