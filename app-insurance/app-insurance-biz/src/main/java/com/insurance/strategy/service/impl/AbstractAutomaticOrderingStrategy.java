package com.insurance.strategy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.insurance.config.RedisService;
import com.insurance.constant.RedisConstant;
import com.insurance.dto.order.CreditCardEncryptionData;
import com.insurance.entity.InsuranceOrder;
import com.insurance.rocketmq.msg.OrderMsg;
import com.insurance.service.InsuranceOrderService;
import com.insurance.strategy.service.ApiEndpointService;
import com.insurance.strategy.service.AutomaticOrderingStrategy;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/5/7
 * @Version 1.0
 * @apiNote:使用模板方法模式： 提供了算法的统一框架，避免了子类重复实现相同的代码。所有的通用流程和逻辑放在父类中，子类只需要关心不同的部分。
 * 新的策略类可以通过继承父类来快速实现，而无需重复编写冗余的代码。只需要关注如何实现具体的业务逻辑。
 * 重复的代码（如订单信息解密、请求发送、日志记录等）都被统一放置在父类中，避免了代码的重复，增强了代码的干净与简洁。
 */
@Configuration
@Slf4j
public abstract class AbstractAutomaticOrderingStrategy implements AutomaticOrderingStrategy {

    protected RedisService redisService;
    protected ApiEndpointService apiEndpointService;
    protected InsuranceOrderService orderService;

    public AbstractAutomaticOrderingStrategy(RedisService redisService,
                                             ApiEndpointService apiEndpointService,
                                             InsuranceOrderService orderService) {
        this.redisService = redisService;
        this.apiEndpointService = apiEndpointService;
        this.orderService = orderService;
    }

    @Override
    @SneakyThrows
    public void sendOrderRequest(InsuranceOrder order) {
        log.info("{}自动下单执行中...", getCompanyCode());

        // 1. 获取 API 地址
        String apiUrl = apiEndpointService.getApiUrl(getCompanyCode());
        if (StringUtils.isBlank(apiUrl)) {
            log.error("{}自动下单失败,apiUrl为空,订单号:{},订单类型id:{}", getCompanyCode(), order.getOrderNum(), order.getProductTypeKey());
            return;
        }

        // 2. 获取订单信息
        String orderInfo = order.getOrderJson();
        if (StringUtils.isBlank(orderInfo)) {
            log.error("{}自动下单失败,订单信息为空,订单号:{}", getCompanyCode(), order.getOrderNum());
            return;
        }

        // 3. 卡号解密
        CreditCardEncryptionData encryptionData = redisService.getObjectFromJson(
                RedisConstant.ORDER_ENCRYPT_CARD + order.getOrderNum(), CreditCardEncryptionData.class);
        if (Objects.isNull(encryptionData) || !encryptionData.isValid()) {
            log.error("{}自动下单失败,加密卡号数据异常,订单号:{}", getCompanyCode(), order.getOrderNum());
            orderService.handleFailOrder(OrderMsg.builder().orderNo(order.getOrderNum()).build(), 2);
            return;
        }

        //4:新版由自动化那边解密，后端只需要传密钥
        JSONObject requestBodyJson = new JSONObject();
        requestBodyJson.put("secret", encryptionData.getSecretKey() + "|" + order.getOrderNum());

        // 5. 异步发送请求（交由子类实现）
//        Object object = jsonRedisService.getObjectFromJson(RedisConstant.ORDER_INFO + encryptionData.getSecretKey(),Object.class);
//        log.info("获取到的订单信息:{}", object);
        log.info("=======================>自动化下单:{}", requestBodyJson);
        sendAsyncRequest(apiUrl, JSONObject.toJSONString(requestBodyJson));

//        以下是旧版逻辑-由后端解密数据,将明文数据直接传到自动化下单那边,不再用
//        String decryptCardNumber = SecureEncryptUtil.decrypt(encryptionData.getEncryptedCardNumber(), encryptionData.getSecretKey());
//        String decryptCvc = SecureEncryptUtil.decrypt(encryptionData.getEncryptedCardNumber(), encryptionData.getSecretKey());
//        encryptionData.setEndingNumber(decryptCvc);
//        log.info("解密后的卡号:{}, 验证通过:{}", decryptCardNumber, encryptionData.getCardNumber().equals(decryptCardNumber));

        // 4. 构造请求体（交由子类实现）
//        String requestBody = buildRequestJson(orderInfo, decryptCardNumber, encryptionData);
//        if (StringUtils.isBlank(requestBody)) {
//            log.error("{}自动下单失败，请求体构造失败，订单号:{}", getCompanyCode(), order.getOrderNum());
//            return;
//        }
//        // 5. 异步发送请求（交由子类实现）
//        sendAsyncRequest(apiUrl, JSONObject.toJSONString(requestBodyJson));


//        以下是本地调试信息-针对新版
//        Object object = redisService.get(RedisConstant.ORDER_INFO + encryptionData.getSecretKey());
//        log.info("获取到的订单信息:{}", object);
//        AllianFormData formData = JSONObject.parseObject(orderInfo, AllianFormData.class);
//        String decryptCardNumber2 = formData.getStep3().getCard_number();
//        String decryptCvc2 = formData.getStep3().getCvc();
//        String decrypt = SecureEncryptUtil.decrypt(decryptCardNumber2, encryptionData.getSecretKey());
//        String cvc = SecureEncryptUtil.decrypt(decryptCvc2, encryptionData.getSecretKey());
//        log.info("解密后的卡号:{}, cvc:{}", decrypt, cvc);
    }

    /**
     * 子类实现构造请求 JSON 的逻辑
     */
    protected abstract String buildRequestJson(String orderInfo, String decryptCard, CreditCardEncryptionData data);

    /**
     * 子类实现实际 HTTP 异步发送
     */
    protected abstract void sendAsyncRequest(String url, String requestBody);
}
