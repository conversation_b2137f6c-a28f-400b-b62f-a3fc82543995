# app-partner异常处理开发规范

**作者**: VON  
**版本**: 1.0  
**更新日期**: 2025-08-06

## 1. 概述

本文档旨在规范app-partner模块的异常处理机制，确保团队在异常处理方面保持一致性和规范性。通过统一的异常处理规范，提高代码质量、系统稳定性和问题排查效率。

## 2. 异常处理架构

### 2.1 核心组件

```
异常处理架构
├── PartnerExceptionInfo (自定义异常类)
├── PartnerErrorEnum (错误码枚举)
├── GlobalExceptionConfig (全局异常处理器)
└── RemoteErrorUtil (远程异常工具类)
```

### 2.2 异常处理流程

```
Controller -> Service -> 抛出异常 -> GlobalExceptionConfig -> 统一响应格式 -> 客户端
```

## 3. 错误码管理规范

### 3.1 错误码分类体系

| 错误码范围 | 业务模块 | 说明 | 示例 |
|-----------|---------|------|------|
| 10086 | 业务校验 | 通用业务校验异常 | 数据不存在、参数校验失败 |
| 500 | 通用业务 | 通用业务异常 | 操作失败、状态异常 |
| 50001-50099 | 用户模块 | 用户相关错误 | 注册失败、密码重置失败 |
| 51001-51099 | 学生模块 | 学生管理相关错误 | 学生信息异常、申请状态错误 |
| 52001-52099 | 代理商模块 | 代理商相关错误 | 代理商不存在、权限不足 |
| 53001-53099 | 佣金模块 | 佣金结算相关错误 | 佣金计算异常、结算失败 |
| 60001-60099 | 文件模块 | 文件处理相关错误 | 文件为空、类型不允许 |
| 70001-70099 | 权限模块 | 权限相关错误 | 角色不存在、权限不足 |

### 3.2 错误码命名规范

```java
public enum PartnerErrorEnum {
    // 格式：[模块前缀]_[具体业务]_[异常类型]
    USER_REGISTER_ERROR(50001, "用户注册失败"),
    USER_CACHE_NOT_FOUND(50002, "用户缓存信息不存在"),
    STUDENT_INFO_INVALID(51001, "学生信息无效"),
    AGENT_NOT_EXISTS(52001, "代理商不存在"),
    COMMISSION_CALC_ERROR(53001, "佣金计算异常"),
    FILE_EMPTY_ERROR(60001, "文件为空"),
    ROLE_NAME_EXISTS(70001, "角色名称已存在");
}
```

### 3.3 错误信息描述标准

- **简洁明了**：错误信息应简洁明了，便于用户理解
- **中文描述**：面向用户的错误信息使用中文
- **无技术细节**：避免暴露系统内部技术细节
- **可操作性**：尽可能提供解决建议

```java
// 好的错误信息
"用户名已存在，请选择其他用户名"
"文件大小超出限制，请上传小于10MB的文件"

// 不好的错误信息  
"Duplicate entry 'username' for key 'uk_username'"
"java.io.IOException: File size exceeds maximum"
```

## 4. 异常抛出规范

### 4.1 Service层异常抛出原则

**4.1.1 业务校验异常**

```java
// 使用错误码枚举（推荐）
if (ObjectUtil.isEmpty(agentId)) {
    throw new PartnerExceptionInfo(
        PartnerErrorEnum.AGENT_NOT_EXISTS.errorCode,
        PartnerErrorEnum.AGENT_NOT_EXISTS.errorMessage
    );
}

// 组合错误信息
if (StringUtils.isBlank(username)) {
    throw new PartnerExceptionInfo(
        PartnerErrorEnum.USER_REGISTER_ERROR.errorCode,
        PartnerErrorEnum.USER_REGISTER_ERROR.errorMessage + ":用户名不能为空"
    );
}
```

**4.1.2 数据存在性校验**

```java
// 检查必要数据是否存在
MPartnerUserEntity user = userMapper.selectById(userId);
if (ObjectUtil.isEmpty(user)) {
    throw new PartnerExceptionInfo(
        PartnerErrorEnum.USER_INFO_ERROR.errorCode,
        "用户信息不存在"
    );
}
```

**4.1.3 业务规则校验**

```java
// 检查业务规则
if (existingCount > 0) {
    throw new PartnerExceptionInfo(
        PartnerErrorEnum.ROLE_NAME_EXISTS.errorCode,
        PartnerErrorEnum.ROLE_NAME_EXISTS.errorMessage
    );
}
```

**4.1.4 权限校验**

```java
// 权限检查
if (!hasPermission(userId, operation)) {
    throw new PartnerExceptionInfo(
        PartnerErrorEnum.ACCESS_DENIED.errorCode,
        "您没有执行此操作的权限"
    );
}
```

### 4.2 Controller层异常处理原则

**4.2.1 不直接处理业务异常**

```java
// 正确做法：让异常向上抛出
@PostMapping("/saveStudent")
public R saveStudent(@RequestBody @Valid MStudentAddOrEditDto dto) {
    studentService.saveOrUpdateStudent(dto); // 可能抛出异常
    return R.ok("保存成功");
}
```

**4.2.2 参数校验**

```java
// 使用@Valid注解进行参数校验
@GetMapping("/getStudentList")
public R getStudentList(@ParameterObject @Valid MStudentParamsDto dto) {
    IPage result = studentService.getStudentList(dto);
    return R.ok(result);
}
```

## 5. 全局异常处理规范

### 5.1 异常处理器配置

```java
@Order(1)
@RestControllerAdvice
public class GlobalExceptionConfig {
    
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionConfig.class);

    // 业务异常处理
    @ExceptionHandler(PartnerExceptionInfo.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R handleBusinessException(PartnerExceptionInfo e) {
        log.warn("业务异常: code={}, message={}", e.getErrorCode(), e.getErrorMessage());
        Tracer.trace(e); // Sentinel监控
        return R.restResult(null, e.getErrorCode(), e.getErrorMessage());
    }
    
    // 参数校验异常处理
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R handleValidationException(MethodArgumentNotValidException e) {
        String errorMessage = e.getBindingResult().getFieldErrors().get(0).getDefaultMessage();
        log.warn("参数校验异常: {}", errorMessage);
        return R.failed(errorMessage);
    }
    
    // 系统异常处理
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R handleSystemException(Exception e) {
        log.error("系统异常: ", e);
        Tracer.trace(e);
        return R.failed("系统繁忙，请稍后重试");
    }
}
```

### 5.2 HTTP状态码使用规范

| HTTP状态码 | 使用场景 | 示例 |
|-----------|---------|------|
| 200 OK | 请求成功 | 正常业务处理成功 |
| 400 Bad Request | 客户端错误 | 参数校验失败、业务异常 |
| 401 Unauthorized | 认证失败 | 未登录、token失效 |
| 403 Forbidden | 权限不足 | 无操作权限 |
| 404 Not Found | 资源不存在 | 接口不存在 |
| 500 Internal Server Error | 服务器错误 | 系统异常、未知错误 |

## 6. 日志记录规范

### 6.1 日志级别使用标准

| 日志级别 | 使用场景 | 记录内容 |
|---------|---------|---------|
| ERROR | 系统异常、严重业务异常 | 完整异常堆栈信息 |
| WARN | 业务异常、参数校验异常 | 异常信息，不包含堆栈 |
| INFO | 重要业务操作 | 关键业务流程节点 |
| DEBUG | 调试信息 | 详细的执行过程 |

### 6.2 日志格式规范

```java
// 业务异常日志
log.warn("业务异常: operation={}, userId={}, error={}", 
    operation, userId, e.getErrorMessage());

// 系统异常日志  
log.error("系统异常: method={}, params={}, error={}", 
    methodName, params, e.getMessage(), e);

// 重要业务操作日志
log.info("用户操作: userId={}, operation={}, result={}", 
    userId, operation, result);
```

### 6.3 敏感信息脱敏

```java
// 密码脱敏
log.info("用户登录: username={}, password={}", 
    username, "******");

// 手机号脱敏  
log.info("发送短信: mobile={}", 
    mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));

// 身份证号脱敏
log.info("实名认证: idCard={}", 
    idCard.replaceAll("(\\d{6})\\d{8}(\\d{4})", "$1********$2"));
```

## 7. 响应格式规范

### 7.1 统一响应格式

```java
// 成功响应
{
    "code": 200,
    "message": "操作成功",
    "data": {...}
}

// 业务异常响应
{
    "code": 50001,
    "message": "用户名已存在",
    "data": null
}

// 系统异常响应
{
    "code": 500,
    "message": "系统繁忙，请稍后重试",
    "data": null
}
```

### 7.2 响应构建规范

```java
// 成功响应
return R.ok(data);
return R.ok(data, "操作成功");

// 失败响应
return R.failed("操作失败");
return R.failed(errorCode, "具体错误信息");

// 自定义响应
return R.restResult(data, errorCode, message);
```

## 8. 微服务异常处理规范

### 8.1 远程调用异常处理

```java
// 使用RemoteErrorUtil处理远程调用异常
try {
    R result = remoteFeignClient.callRemoteService(params);
    if (!result.isSuccess()) {
        String errorMsg = RemoteErrorUtil.extractRemoteErrorMsg(result.getMessage());
        throw new PartnerExceptionInfo(
            PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode,
            "远程服务调用失败:" + errorMsg
        );
    }
    return result.getData();
} catch (Exception e) {
    log.error("远程服务调用异常: service={}, error={}", serviceName, e.getMessage());
    throw new PartnerExceptionInfo(
        PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode,
        "服务暂时不可用，请稍后重试"
    );
}
```

### 8.2 服务降级异常处理

```java
@Component
public class RemoteServiceFallback implements RemoteFeignClient {
    
    @Override
    public R callRemoteService(Object params) {
        log.warn("远程服务降级: service={}", "RemoteService");
        return R.failed(
            PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode,
            "服务暂时不可用，请稍后重试"
        );
    }
}
```

## 9. 开发最佳实践

### 9.1 异常处理代码示例

**9.1.1 标准的Service方法异常处理**

```java
@Service
public class StudentServiceImpl implements IStudentService {
    
    @Override
    public void saveStudent(MStudentAddOrEditDto dto) {
        // 1. 参数校验
        validateStudentParams(dto);
        
        // 2. 业务规则校验
        checkBusinessRules(dto);
        
        // 3. 执行业务逻辑
        try {
            doSaveStudent(dto);
        } catch (Exception e) {
            log.error("保存学生信息失败: dto={}, error={}", dto, e.getMessage(), e);
            throw new PartnerExceptionInfo(
                PartnerErrorEnum.STUDENT_SAVE_ERROR.errorCode,
                "保存学生信息失败"
            );
        }
    }
    
    private void validateStudentParams(MStudentAddOrEditDto dto) {
        if (StringUtils.isBlank(dto.getStudentName())) {
            throw new PartnerExceptionInfo(
                PartnerErrorEnum.STUDENT_INFO_INVALID.errorCode,
                "学生姓名不能为空"
            );
        }
    }
    
    private void checkBusinessRules(MStudentAddOrEditDto dto) {
        // 检查学生是否已存在
        if (existsStudent(dto.getStudentName(), dto.getIdCard())) {
            throw new PartnerExceptionInfo(
                PartnerErrorEnum.STUDENT_ALREADY_EXISTS.errorCode,
                "学生信息已存在"
            );
        }
    }
}
```

**9.1.2 事务回滚异常处理**

```java
@Transactional(rollbackFor = Exception.class)
@Override
public void batchSaveStudents(List<MStudentAddOrEditDto> students) {
    try {
        for (MStudentAddOrEditDto student : students) {
            saveStudent(student);
        }
    } catch (PartnerExceptionInfo e) {
        // 业务异常直接抛出，触发事务回滚
        throw e;
    } catch (Exception e) {
        log.error("批量保存学生信息失败: error={}", e.getMessage(), e);
        // 系统异常包装后抛出
        throw new PartnerExceptionInfo(
            PartnerErrorEnum.BATCH_SAVE_ERROR.errorCode,
            "批量保存失败"
        );
    }
}
```

### 9.2 常见错误场景处理方案

**9.2.1 数据库操作异常**

```java
// 主键冲突
catch (DuplicateKeyException e) {
    throw new PartnerExceptionInfo(
        PartnerErrorEnum.DATA_DUPLICATE_ERROR.errorCode,
        "数据已存在"
    );
}

// 外键约束异常
catch (DataIntegrityViolationException e) {
    throw new PartnerExceptionInfo(
        PartnerErrorEnum.DATA_INTEGRITY_ERROR.errorCode,
        "数据完整性约束失败"
    );
}
```

**9.2.2 并发操作异常**

```java
// 乐观锁异常
catch (OptimisticLockException e) {
    throw new PartnerExceptionInfo(
        PartnerErrorEnum.CONCURRENT_UPDATE_ERROR.errorCode,
        "数据已被其他用户修改，请刷新后重试"
    );
}
```

### 9.3 性能优化建议

**9.3.1 避免异常影响性能**

```java
// 不要使用异常控制正常业务流程
// 错误做法
try {
    return studentList.get(index);
} catch (IndexOutOfBoundsException e) {
    return null;
}

// 正确做法
if (index >= 0 && index < studentList.size()) {
    return studentList.get(index);
}
return null;
```

**9.3.2 合理使用异常日志**

```java
// 避免在循环中记录异常日志
List<String> errorMessages = new ArrayList<>();
for (StudentDto student : students) {
    try {
        validateStudent(student);
    } catch (Exception e) {
        errorMessages.add(e.getMessage());
    }
}
if (!errorMessages.isEmpty()) {
    log.warn("批量校验异常: errors={}", errorMessages);
}
```

## 10. 测试和监控规范

### 10.1 异常场景测试要求

**10.1.1 单元测试**

```java
@Test
public void testSaveStudent_WithInvalidParams_ShouldThrowException() {
    // 准备测试数据
    MStudentAddOrEditDto dto = new MStudentAddOrEditDto();
    dto.setStudentName(""); // 空字符串
    
    // 执行并验证异常
    PartnerExceptionInfo exception = assertThrows(
        PartnerExceptionInfo.class, 
        () -> studentService.saveStudent(dto)
    );
    
    assertEquals(PartnerErrorEnum.STUDENT_INFO_INVALID.errorCode, 
                exception.getErrorCode());
    assertTrue(exception.getErrorMessage().contains("学生姓名不能为空"));
}
```

**10.1.2 集成测试**

```java
@Test
public void testSaveStudent_Integration() {
    // 测试正常流程
    // 测试异常流程
    // 测试边界条件
}
```

### 10.2 生产环境监控配置

**10.2.1 异常告警规则**

```yaml
# 告警配置示例
alerts:
  - name: "业务异常告警"
    condition: "error_rate > 5%"
    duration: "5m"
    
  - name: "系统异常告警"  
    condition: "system_error_count > 10"
    duration: "1m"
```

**10.2.2 异常统计指标**

- 异常总数和频率
- 异常类型分布
- 异常响应时间
- 异常影响范围

## 11. 总结

本规范基于app-partner项目现有的异常处理模式，提供了完整的异常处理开发指导。遵循本规范可以：

1. **提高代码质量**：统一的异常处理模式提高代码一致性
2. **改善用户体验**：友好的错误提示和适当的HTTP状态码
3. **便于问题定位**：完善的日志记录和监控体系
4. **降低维护成本**：标准化的异常处理减少维护工作量

团队成员在开发过程中应严格遵循本规范，确保系统的稳定性和可维护性。