INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_APPLICATION_APPROVED_HAS_ACCOUNT', '<div class="content"><h2 class="title">代理申请通过通知(${personalName})</h2><div class="border"></div></br><div class="desc"><div>${personalName}，您好:</br>申请代理:${name}</br>您提交的申请已审核通过，请您登录小程序完成合同签署</br>以下为华通伙伴小程序的初始账号和密码:</br>账号：${account}</br>密码：${password}</br>若您使用的是电脑查看邮件，请扫下方小程序码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div></div>', '<div class="content"><h2 class="title">Agent Application Approval Notification(${personalName})</h2><div class="border"></div></br><div class="desc"><div>Dear ${personalName},</br>Agent Application: ${name}</br>Your application has been approved. Please log in to the mini-program to complete the contract signing.</br>Here are the initial account credentials for the HTI Partner Mini-Program:</br>Account: ${account}</br>Password: ${password}</br>If you are viewing this email on a computer, please scan the mini-program QR code below: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">HTI International</br>${currentDate}</br></div></div>', '代理申请通过有账号', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_APPLICATION_APPROVED_NO_ACCOUNT', '<div class="content"><h2 class="title">代理申请通过通知(${personalName})</h2><div class="border"></div></br><div class="desc"><div>${personalName}，您好:</br>申请代理:${name}</br>您提交的申请已审核通过，请您登录小程序完成合同签署</br>若您使用的是电脑查看邮件，请扫下方小程序码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div></div>', '<div class="content"><h2 class="title">Agent Application Approval Notification(${personalName})</h2><div class="border"></div></br><div class="desc"><div>Dear ${personalName},</br>Agent Application: ${name}</br>Your application has been approved. Please log in to the mini-program to complete the contract signing.</br>If you are viewing this email on a computer, please scan the mini-program QR code below: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">HTI International</br>${currentDate}</br></div></div>', '代理申请通过无账号', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_APPLICATION_REJECTED', '<div class="content"><h2 class="title">代理申请审批意见通知(${personalName})</h2><div class="border"></div></br><div class="desc"><div>${personalName}，您好:</br>申请代理:${name}</br>审批意见如下：资料审核不通过请您重新提交资料</br>在线表单修改：<a href="https://app.ht-international.online/apply-agent-online-form/index?sign=${id}">https://app.ht-international.online/apply-agent-online-form/index?sign=${id}</a></br>小程序二维码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div></div>', '<div class="content"><h2 class="title">Agent Application Review Notice(${personalName})</h2><div class="border"></div></br><div class="desc"><div>Dear ${personalName},</br>Agent Application: ${name}</br>Review result: Application materials review failed, please resubmit your materials</br>Online form modification: <a href="https://app.ht-international.online/apply-agent-online-form/index?sign=${id}">https://app.ht-international.online/apply-agent-online-form/index?sign=${id}</a></br>Mini program QR code: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">HTI International</br>${currentDate}</br></div></div>', '代理申请不通过', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_RENEWAL_APPROVED_HAS_ACCOUNT', '<div class="content"><h2 class="title">代理续签申请通过通知(${personalName})</h2><div class="border"></div></br><div class="desc"><div>${personalName}，您好:</br>申请代理:${name}</br>您提交的续签申请已审核通过，请您登录小程序完成合同签署</br>以下为华通伙伴小程序的初始账号和密码:</br>账号：${account}</br>密码：${password}</br>若您使用的是电脑查看邮件，请扫下方小程序码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div></div>', '<div class="content"><h2 class="title">Agent Renewal Application Approval Notification(${personalName})</h2><div class="border"></div></br><div class="desc"><div>Dear ${personalName},</br>Agent Application: ${name}</br>Your renewal application has been approved. Please log in to the mini-program to complete the contract signing.</br>Here are the initial account credentials for the HTI Partner Mini-Program:</br>Account: ${account}</br>Password: ${password}</br>If you are viewing this email on a computer, please scan the mini-program QR code below: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">HTI International</br>${currentDate}</br></div></div>', '代理续签申请通过有账号', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_RENEWAL_APPROVED_NO_ACCOUNT', '<div class="content"><h2 class="title">代理续签申请通过通知(${personalName})</h2><div class="border"></div></br><div class="desc"><div>${personalName}，您好:</br>申请代理:${name}</br>您提交的续签申请已审核通过，请您登录小程序完成合同签署</br>若您使用的是电脑查看邮件，请扫下方小程序码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div></div>', '<div class="content"><h2 class="title">Agent Renewal Application Approval Notification(${personalName})</h2><div class="border"></div></br><div class="desc"><div>Dear ${personalName},</br>Agent Application: ${name}</br>Your renewal application has been approved. Please log in to the mini-program to complete the contract signing.</br>If you are viewing this email on a computer, please scan the mini-program QR code below: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">HTI International</br>${currentDate}</br></div></div>', '代理续签申请通过无账号', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_RENEWAL_REJECTED', '<div class="content"><h2 class="title">代理续签审批意见通知(${personalName})</h2><div class="border"></div></br><div class="desc"><div>${personalName}，您好:</br>申请代理:${name}</br>审批意见如下：资料审核不通过请您重新提交资料</br>在线表单修改：<a href="https://app.ht-international.online/apply-agent-online-form/renewal?id=${id}">https://app.ht-international.online/apply-agent-online-form/renewal?id=${id}</a></br>小程序二维码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div></div>', '<div class="content"><h2 class="title">Agent Renewal Review Notice(${personalName})</h2><div class="border"></div></br><div class="desc"><div>Dear ${personalName},</br>Agent Application: ${name}</br>Review result: Application materials review failed, please resubmit your materials</br>Online form modification: <a href="https://app.ht-international.online/apply-agent-online-form/renewal?id=${id}">https://app.ht-international.online/apply-agent-online-form/renewal?id=${id}</a></br>Mini program QR code: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">HTI International</br>${currentDate}</br></div></div>', '代理续签申请不通过', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'CONTRACT_APPROVAL_PASSED_HAS_ACCOUNT', '<div class="content">    <h2 class="title">诚邀您使用由华通国际推出的华通伙伴小程序!</h2>    <div class="border"></div></br>    <div class="desc">        <div>    为了顾问将更多的精力聚焦于拓展客户与提升服务质量这些核心要务上，我们推出华通伙伴小程序，助力我们的合作伙伴更快捷的完成业务。</br>    已为您开通 ${name} 登录华通伙伴小程序的账户，以下为华通伙伴小程序的初始账号和密码：</br>    账号：${account}</br>    密码：${password}</br>    若您使用的是电脑查看邮件，请扫下方小程序码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br>        </div>    </div>    <div style="text-align: right;" class="desc">        华通国际</br>        ${currentDate}</br>    </div></div>', '<div class="content">    <h2 class="title">We sincerely invite you to use the HTI Partner Mini-Program launched by HTI International!</h2>    <div class="border"></div></br>    <div class="desc">        <div>    To help consultants focus more energy on core tasks such as expanding customers and improving service quality, we have launched the HTI Partner Mini-Program to help our partners complete business more quickly.</br>    We have opened the ${name} account for logging into the HTI Partner Mini-Program. Here are the initial account credentials for the HTI Partner Mini-Program:</br>    Account: ${account}</br>    Password: ${password}</br>    If you are viewing this email on a computer, please scan the mini-program QR code below: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br>        </div>    </div>    <div style="text-align: right;" class="desc">        HTI International</br>        ${currentDate}</br>    </div></div>', '合同审批通过有账号', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'CONTRACT_APPROVAL_PASSED_NO_ACCOUNT', '<div class="content">    <h2 class="title">诚邀您使用由华通国际推出的华通伙伴小程序!</h2>    <div class="border"></div></br>    <div class="desc">        <div>    为了顾问将更多的精力聚焦于拓展客户与提升服务质量这些核心要务上，我们推出华通伙伴小程序，助力我们的合作伙伴更快捷的完成业务。</br>    已为您开通 ${personalName} 登录华通伙伴小程序的账户，请使用原密码登录，以下为华通伙伴小程序的账号：</br>    账号：${account}</br>    若您使用的是电脑查看邮件，请扫下方小程序码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br>        </div>    </div>    <div style="text-align: right;" class="desc">        华通国际</br>        ${currentDate}</br>    </div></div>', '<div class="content">    <h2 class="title">We sincerely invite you to use the HTI Partner Mini-Program launched by HTI International!</h2>    <div class="border"></div></br>    <div class="desc">        <div>    To help consultants focus more energy on core tasks such as expanding customers and improving service quality, we have launched the HTI Partner Mini-Program to help our partners complete business more quickly.</br>    We have opened the ${personalName} account for logging into the HTI Partner Mini-Program. Please use your original password to log in. Here is the account for the HTI Partner Mini-Program:</br>    Account: ${account}</br>    If you are viewing this email on a computer, please scan the mini-program QR code below: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br>        </div>    </div>    <div style="text-align: right;" class="desc">        HTI International</br>        ${currentDate}</br>    </div></div>', '合同审批通过无账号', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_CONTRACT_APPROVAL_PASSED', '<div class="content"><h2 class="title">关于HTI合作协议审核通过通知(${personalName})</h2><div class="border"></div></br><div class="desc"><div>${personalName}，您好:</br>申请代理:${name}</br>您提交的与我方签订的合作协议，经我方相关业务部门审核，已完成全部流程。该合同符合双方约定及公司内部规范，现已正式生效。感谢您方对此次合作的积极推进。</br>同时，HTI Agent小程序已正式开放，您可使用小程序查询相关信息以及提交学生申请、结佣等功能。</br>如有疑问，请询问相关负责的BD。</br>小程序二维码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div></div>', '<div class="content"><h2 class="title">HTI Cooperation Agreement Approval Notice(${personalName})</h2><div class="border"></div></br><div class="desc"><div>Dear ${personalName},</br>Agent Application: ${name}</br>The cooperation agreement you submitted with us has been reviewed by our relevant business departments and all processes have been completed. This contract complies with the agreement between both parties and our company\'s internal regulations, and has now officially taken effect. Thank you for your active promotion of this cooperation.</br>At the same time, the HTI Agent mini-program has been officially opened. You can use the mini-program to query relevant information and submit student applications, commission settlements and other functions.</br>If you have any questions, please consult the relevant BD.</br>Mini-program QR code: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">HTI International</br>${currentDate}</br></div></div>', '代理合同审批通过', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'CONTRACT_APPROVAL_REJECTED', '<div class="content"><h2 class="title">关于HTI合作协议驳回通知(${personalName})</h2><div class="border"></div></br><div class="desc"><div>${personalName}，您好:</br>申请代理:${name}</br>您提交的与我方签订的合作协议，经我方相关业务部门审核，需要进行更改，现已驳回。</br>请通过链接或扫描下方小程序码前往HTI Agent进行更新并重新提交。</br>链接：<a href="https://ais.ht-international.online/index">https://ais.ht-international.online/index</a></br>小程序二维码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div></div>', '<div class="content"><h2 class="title">HTI Cooperation Agreement Rejection Notice(${personalName})</h2><div class="border"></div></br><div class="desc"><div>Dear ${personalName},</br>Agent Application: ${name}</br>The cooperation agreement you submitted with us has been reviewed by our relevant business departments and needs to be modified. It has now been rejected.</br>Please go to HTI Agent through the link or scan the mini-program QR code below to update and resubmit.</br>Link: <a href="https://ais.ht-international.online/index">https://ais.ht-international.online/index</a></br>Mini-program QR code: <img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br></div></div><div style="text-align: right;" class="desc">HTI International</br>${currentDate}</br></div></div>', '合作协议驳回', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_CONTRACT_RENEWAL', '<div class="content"><h2 class="title">华通国际代理合同续签</h2><div class="border"></div></br><div class="desc">  <div>	${name}，您好:</br>	申请代理:${name}</br>	现诚挚邀请您办理合同续签手续，请使用手机微信扫二维码或点击链接进行续签操作。</br>	在线表单链接：<a href="https://app.ht-international.online/apply-agent-online-form/renewal?id=${id}&renewalToken=${renewalToken}">https://app.ht-international.online/apply-agent-online-form/renewal?id=${id}&renewalToken=${renewalToken}</a></br>	小程序二维码：<img src="${qrcode}" alt="hit" width="120" height="120"></br>  </div></div></div>', ' <div class="content">  <h2 class="title">HTI International Agent Contract Renewal</h2>  <div class="border"></div></br>  <div class="desc">    <div>      Dear ${name},</br>      Agent Application: ${name}</br>      We cordially invite you to complete the contract renewal procedures. Please use WeChat to scan the QR code or click the link to  perform the renewal operation.</br>      Online form link: <a href="https://app.ht-international.online/apply-agent-online-form/renewal?id=${id}&renewalToken=${renewalToken}">https://app.ht-international.online/apply-agent-online-form/renewal?id=${id}&renewalToken=${renewalToken}</a></br>      Mini-program QR code: <img src="${qrcode}" alt="HTI Mini Program QR Code" width="120" height="120"></br>    </div>  </div>  </div>', '代理合同续签', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_APPLICATION_SUBMITTED', '<div class="content"><h2 class="title">尊敬的合作方，您好！</h2><div class="border"></div></br><div class="desc"><div>尊敬的合作伙伴：</br>您已使用在线表/小程序成功提交了合作申请。在人工审核前，若内容有所变动或需要补充，您可以点击或复制以下链接在浏览器重新打开申请资料进行修改：</br><a href="https://app.ht-international.online/apply-agent-online-form/index?sign=${id}">https://app.ht-international.online/apply-agent-online-form/index?sign=${id}</a></br>您可以打开微信扫二维码重新打开申请资料进行修改：</br><img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br>谢谢您的申请，我们会尽快审核，其他与您的合作。</br></div></div><div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div></div>', '<div class="content"><h2 class="title">Dear Partner, Hello!</h2><div class="border"></div></br><div class="desc"><div>Dear Partner:</br>You have successfully submitted your cooperation application using the online form/mini-program. Before manual review, if there are any changes or additions to the content, you can click or copy the following link to reopen the application materials in your browser for modifications:</br><a href="https://app.ht-international.online/apply-agent-online-form/index?sign=${id}">https://app.ht-international.online/apply-agent-online-form/index?sign=${id}</a></br>You can open WeChat to scan the QR code to reopen the application materials for modifications:</br><img src="${qrcode}" alt="WeChat Mini Program QR Code" width="120" height="120"></br>Thank you for your application, we will review it as soon as possible, and look forward to our cooperation.</br></div></div><div style="text-align: right;" class="desc">HTI International</br>${currentDate}</br></div></div>', '代理申请提交确认', NULL, NULL, NULL, NULL);
INSERT INTO `ais_reminder_center`.`u_email_template` (`fk_parent_email_template_id`, `email_type_key`, `email_template`, `email_template_en`, `remark`, `gmt_create`, `gmt_create_user`, `gmt_modified`, `gmt_modified_user`) VALUES (1, 'AGENT_APPLICATION_APPROVE_NOTICE', '<div class="content"><h2 class="title">代理申请审批意见通知${personalName}</h2>    <div class="border"></div>    </br>    <div class="desc">        <div>${staffName}，您好:</br>申请代理:${personalName}</br>审批意见如下：${approveComment}</br>在线表单链接：<a href="https://app.ht-international.online/apply-agent-online-form/index?sign=${id}">https://app.ht-international.online/apply-agent-online-form/index?sign=${id}</a></br>小程序二维码：<img src="${qrcode}" alt="微信小程序二维码"                                                                                                                                                                                                                                                            width="120" height="120"></br></div>        <div style="text-align: right;" class="desc">提交时间：${currentTime}</br></div>    </div></div>', '<div class="content"><h2 class="title">Agent Application Review Notice ${personalName}</h2>      <div class="border"></div>      </br>      <div class="desc">          <div>Dear ${staffName},</br>Agent Application: ${personalName}</br>Review result: ${approveComment}</br>Online form link: <a  href="https://app.ht-international.online/apply-agent-online-form/index?sign=${id}">https://app.ht-international.online/apply-agent-online-form/index?sign=${id}</a></br>Mini-program QR        code: <img src="${qrcode}" alt="WeChat Mini Program QR Code"                                                                                                                    width="120"  height="120"></br></div>          <div style="text-align: right;" class="desc">Submission Time : ${currentTime}</br></div>      </div>  </div>', '代理申请审批意见通知', NULL, NULL, NULL, NULL);