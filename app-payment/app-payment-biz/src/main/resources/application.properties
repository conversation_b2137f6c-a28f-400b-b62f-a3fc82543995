# MyBatis Plus 日志配置
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# 服务端口配置
server.port=8084

# Nacos 配置
spring.cloud.nacos.username=nacos
spring.cloud.nacos.password=nacos

# Nacos 注册中心配置
spring.cloud.nacos.discovery.server-addr=127.0.0.1:28848
spring.cloud.nacos.discovery.namespace=test

# Nacos 配置中心配置
spring.cloud.nacos.config.server-addr=127.0.0.1:28848
spring.cloud.nacos.config.namespace=test

# Spring 配置导入
spring.config.import[0]=optional:nacos:<EMAIL>@.yml
spring.config.import[1]=optional:nacos:<EMAIL>@.yml
