# `getPeopleStudentList` 方法的SQL逻辑详细解读

**文件位置：** `app-partner/app-partner-biz/src/main/java/com/partner/service/impl/MStudentServiceImpl.java:158`

**作者：** VON  
**创建时间：** 2025-01-28  
**分析目标：** 深度解析 `getPeopleStudentList` 方法的复杂SQL嵌套逻辑

---

## 1. Java层面逻辑分析

首先，让我们从Java方法层面理解整体流程：

```java
public IPage getPeopleStudentList(Page page, MStudentParamsDto dto) {
    // 1. 权限检查和参数转换
    MStudentParams params = resultParams(dto);
    if (params.getStudentFlag()) {
        return new Page(); // 无权限直接返回空页面
    }
    
    // 2. 调用Mapper获取学生申请步骤列表
    List<MStudentStepList> studentList = mStudentMapper.getPeopleStudentList(params);
    
    // 3. 数据处理：按学生UUID分组
    Map<String, List<MStudentStepList>> groupbylist = 
        studentList.stream().collect(Collectors.groupingBy(MStudentStepList::getStudentUUID));
    
    // 4. 去重并构建返回对象
    Set studentids = new HashSet();
    for (MStudentStepList po : studentList) {
        if (!studentids.contains(po.getStudentUUID())) {
            // 设置学生基本信息
            // 设置申请步骤信息（最多显示前2个）
            studentids.add(po.getStudentUUID());
        }
    }
    
    // 5. 内存分页处理
    return getPages(currentPage, pageSize, resultStudents);
}
```

### 1.1 方法执行流程

1. **权限验证**：通过 `resultParams()` 方法检查用户权限
2. **SQL查询**：调用Mapper执行复杂的多表连接查询
3. **数据分组**：按学生UUID将申请记录分组
4. **去重处理**：确保每个学生只出现一次
5. **数据聚合**：为每个学生聚合申请步骤信息
6. **分页处理**：在内存中进行分页

---

## 2. SQL查询深度解析

### 2.1 主查询结构（MStudentMapper.xml第194-247行）

```sql
SELECT 
    studentUuid.fk_student_uuid AS studentUUID,           -- 学生UUID
    student.name AS studentName,                          -- 学生姓名
    '' AS followName,                                     -- 跟进人姓名（空）
    offerItem.fk_student_offer_id AS studentOfferId,     -- 申请ID
    offerItem.fk_institution_id AS institutionId,        -- 院校ID
    institution.name AS institutionNameChn,              -- 院校英文名
    institution.name_chn AS institutionName,             -- 院校中文名
    offerItemStep.id AS stepid,                          -- 步骤ID
    offerItemStep.step_order AS stepOrder,               -- 步骤顺序
    offerItemStep.step_name,                             -- 步骤名称
    institutionCourse.name AS courseName,                -- 课程名称
    uAreaCountry.name_chn AS countryName,                -- 国家中文名
    
    -- 子查询1：获取合作伙伴用户中文名（多个用逗号分隔）
    (SELECT GROUP_CONCAT(mPartnerUser.name) 
     FROM app_partner_center.r_partner_user_student rPartnerUserStudent
     LEFT JOIN app_partner_center.m_partner_user mPartnerUser 
         ON rPartnerUserStudent.fk_partner_user_id = mPartnerUser.id
     WHERE rPartnerUserStudent.fk_student_id = student.id 
         AND rPartnerUserStudent.is_active = 1
    ) AS partnerUserName,
    
    -- 子查询2：获取合作伙伴用户英文名（多个用逗号分隔）
    (SELECT GROUP_CONCAT(IFNULL(mPartnerUser.name_en, mPartnerUser.name)) 
     FROM app_partner_center.r_partner_user_student rPartnerUserStudent
     LEFT JOIN app_partner_center.m_partner_user mPartnerUser 
         ON rPartnerUserStudent.fk_partner_user_id = mPartnerUser.id
     WHERE rPartnerUserStudent.fk_student_id = student.id 
         AND rPartnerUserStudent.is_active = 1
    ) AS partnerUserNameEn

FROM ais_sale_center.m_student_offer_item offerItem
```

### 2.2 表连接关系解析

```sql
-- 1. 权限控制连接（最关键的部分）
<include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>

-- 2. 核心业务表连接
INNER JOIN ais_sale_center.m_student student 
    ON offerItem.fk_student_id = student.id                    -- 学生表

INNER JOIN ais_sale_center.r_student_uuid studentUuid 
    ON studentUuid.fk_student_id = student.id                  -- 学生UUID关系表

INNER JOIN ais_institution_center.m_institution institution 
    ON institution.id = offerItem.fk_institution_id            -- 院校表

INNER JOIN ais_sale_center.u_student_offer_item_step offerItemStep 
    ON offerItemStep.id = offerItem.fk_student_offer_item_step_id  -- 申请步骤表

-- 3. 可选连接
LEFT JOIN ais_institution_center.m_institution_course institutionCourse 
    ON institutionCourse.id = offerItem.fk_institution_course_id   -- 课程表

LEFT JOIN ais_institution_center.u_area_country uAreaCountry 
    ON offerItem.fk_area_country_id = uAreaCountry.id             -- 国家表
```

### 2.3 涉及的数据库和表说明

| 数据库 | 表名 | 用途说明 |
|--------|------|----------|
| `ais_sale_center` | `m_student_offer_item` | 学生申请项目主表 |
| `ais_sale_center` | `m_student` | 学生基本信息表 |
| `ais_sale_center` | `r_student_uuid` | 学生UUID关系表 |
| `ais_sale_center` | `u_student_offer_item_step` | 申请步骤字典表 |
| `ais_sale_center` | `m_app_student` | 应用学生信息表 |
| `ais_institution_center` | `m_institution` | 院校信息表 |
| `ais_institution_center` | `m_institution_course` | 院校课程表 |
| `ais_institution_center` | `u_area_country` | 国家字典表 |
| `app_partner_center` | `r_partner_user_student` | 合作伙伴用户学生关系表 |
| `app_partner_center` | `m_partner_user` | 合作伙伴用户表 |

---

## 3. 权限控制核心逻辑解析

最复杂的部分是权限控制SQL片段`offerItemPermissionRoleSql`：

```sql
INNER JOIN (
    SELECT DISTINCT offerItem.id AS offerItemId
    FROM ais_sale_center.m_student_offer_item AS offerItem
    
    -- 个人角色权限控制（关键逻辑）
    <if test="roleTypeFlag==true">
        INNER JOIN (
            -- 方式1：通过合作伙伴用户分配的学生
            SELECT DISTINCT fk_student_id 
            FROM app_partner_center.r_partner_user_student
            WHERE is_active=1 AND fk_partner_user_id = #{partnerUserId}
            
            UNION
            
            -- 方式2：通过应用平台直接创建的学生
            SELECT DISTINCT fk_student_id 
            FROM ais_sale_center.m_app_student
            WHERE fk_platform_create_user_id = #{partnerUserId} 
                AND fk_platform_code = 'PARTNER' 
                AND status = 2
        ) distributionStudent ON offerItem.fk_student_id = distributionStudent.fk_student_id
    </if>
    
    -- 基础过滤条件
    WHERE offerItem.status = 1              -- 有效状态
        AND offerItem.is_follow = 0         -- 非跟进状态
        AND offerItem.is_follow_hidden = 0  -- 非隐藏跟进
        AND offerItem.fk_agent_id = #{agentId}  -- 指定代理商
) permission ON offerItem.id = permission.offerItemId
```

### 3.1 权限控制逻辑说明

**双重权限验证机制：**

1. **分配权限**：通过`r_partner_user_student`表检查用户是否被分配了该学生
   - `is_active=1`：确保分配关系有效
   - `fk_partner_user_id`：匹配当前用户ID

2. **创建权限**：通过`m_app_student`表检查用户是否是该学生申请的创建者
   - `fk_platform_create_user_id`：匹配创建用户ID
   - `fk_platform_code='PARTNER'`：确保来源是合作伙伴平台
   - `status=2`：确保是已审核通过的学生

**状态过滤条件：**
- `status = 1`：只显示有效的申请项目
- `is_follow = 0`：排除跟进中的项目
- `is_follow_hidden = 0`：排除隐藏的跟进项目
- `fk_agent_id`：确保属于当前代理商

### 3.2 权限控制流程图

```
用户请求查询
    ↓
检查roleTypeFlag
    ↓
roleTypeFlag=true? 
    ↓
   是    否
    ↓     ↓
个人权限   全量权限
    ↓     ↓
查询分配的学生 + 创建的学生 → 基础过滤条件
    ↓
返回有权限的申请项目ID
    ↓
与主查询JOIN获取详细信息
```

---

## 4. 动态查询条件

```sql
WHERE 1=1
<if test="countryId != null">
    AND offerItem.fk_area_country_id = #{countryId}  -- 国家筛选
</if>
<if test="year != 0">
    AND YEAR(offerItem.gmt_create) = #{year}         -- 年份筛选
</if>
<if test="studentName!=null and studentName!= ''">
    AND (
        student.name LIKE CONCAT('%',#{studentName},'%')                    -- 学生中文名
        OR REPLACE(CONCAT(IFNULL(student.last_name,''),IFNULL(student.first_name,'')),' ','') 
           LIKE CONCAT('%',#{studentName},'%')                              -- 姓+名
        OR REPLACE(CONCAT(IFNULL(student.first_name,''),IFNULL(student.last_name,'')),' ','') 
           LIKE CONCAT('%',#{studentName},'%')                              -- 名+姓
        OR institution.name LIKE CONCAT('%',#{studentName},'%')             -- 院校英文名
        OR institution.name_chn LIKE CONCAT('%',#{studentName},'%')         -- 院校中文名
        OR institutionCourse.name LIKE CONCAT('%',#{studentName},'%')       -- 课程名称
    )
</if>
ORDER BY student.id DESC, offerItemStep.step_order ASC  -- 学生ID倒序，步骤顺序正序
```

### 4.1 搜索逻辑说明

**学生姓名搜索的6种匹配方式：**
1. 直接匹配学生中文姓名
2. 组合匹配：姓+名（去除空格）
3. 组合匹配：名+姓（去除空格）
4. 匹配院校英文名称
5. 匹配院校中文名称
6. 匹配课程名称

这种设计确保了用户无论输入什么关键词，都能尽可能找到相关的学生记录。

---

## 5. 子查询详解

### 5.1 合作伙伴用户信息查询

```sql
-- 获取中文名
(SELECT GROUP_CONCAT(mPartnerUser.name) 
 FROM app_partner_center.r_partner_user_student rPartnerUserStudent
 LEFT JOIN app_partner_center.m_partner_user mPartnerUser 
     ON rPartnerUserStudent.fk_partner_user_id = mPartnerUser.id
 WHERE rPartnerUserStudent.fk_student_id = student.id 
     AND rPartnerUserStudent.is_active = 1
) AS partnerUserName

-- 获取英文名（优先使用name_en，没有则使用name）
(SELECT GROUP_CONCAT(IFNULL(mPartnerUser.name_en, mPartnerUser.name)) 
 FROM app_partner_center.r_partner_user_student rPartnerUserStudent
 LEFT JOIN app_partner_center.m_partner_user mPartnerUser 
     ON rPartnerUserStudent.fk_partner_user_id = mPartnerUser.id
 WHERE rPartnerUserStudent.fk_student_id = student.id 
     AND rPartnerUserStudent.is_active = 1
) AS partnerUserNameEn
```

**设计说明：**
- 使用`GROUP_CONCAT`处理一个学生对应多个合作伙伴用户的情况
- 通过`is_active=1`确保只获取活跃的分配关系
- 英文名有优先级：优先使用专门的英文名字段，没有则回退到普通名称

---

## 6. 整体业务逻辑总结

### 6.1 业务目标
这个方法用于获取**按人维度**的学生申请列表，主要用于：
- 显示学生的基本信息
- 展示每个学生的申请进度（步骤）
- 支持多维度筛选（国家、年份、学生姓名等）
- 实现精确的权限控制

### 6.2 数据流转过程

```
用户请求 
  ↓
权限验证（roleTypeFlag判断）
  ↓
SQL查询（多表连接+权限过滤）
  ↓
数据按学生UUID分组
  ↓
去重处理（每个学生只显示一次）
  ↓
步骤信息聚合（显示前2个步骤）
  ↓
内存分页
  ↓
返回结果
```

### 6.3 权限控制机制

**三层权限控制：**
1. **Java层权限检查**：`resultParams()`方法检查用户是否有查看权限
2. **SQL层角色权限**：通过`roleTypeFlag`控制查看范围
3. **数据层代理商权限**：确保只能查看自己代理商的数据

### 6.4 核心特点

1. **复杂的多表关联**：涉及学生、申请、院校、课程、国家等多个业务实体
2. **嵌套子查询**：用于获取合作伙伴用户信息
3. **权限精确控制**：确保用户只能看到有权限的数据
4. **灵活的动态筛选**：支持多种查询条件组合
5. **数据去重聚合**：Java层面进行分组和去重处理

---

## 7. 性能考虑

### 7.1 潜在性能问题

1. **子查询性能**：每行记录都执行两次子查询获取合作伙伴用户信息
2. **权限查询复杂度**：权限控制涉及多表JOIN和UNION操作
3. **内存分页**：在Java内存中进行分页，大数据量时可能有性能问题

### 7.2 优化建议

1. **子查询优化**：考虑将子查询改为LEFT JOIN，减少执行次数
2. **权限查询缓存**：可以考虑将用户权限信息缓存
3. **数据库分页**：考虑在SQL层面实现分页，减少内存压力

---

## 8. 总结

这个SQL设计体现了企业级应用中典型的**数据权限控制**和**复杂业务查询**的实现方式，通过多层嵌套和权限验证确保数据安全性和查询准确性。

**主要亮点：**
- 完善的权限控制体系
- 灵活的动态查询条件
- 全面的数据关联展示
- 合理的数据处理流程

**改进空间：**
- 查询性能优化
- 分页机制改进  
- 权限判断逻辑简化

这种设计在保证功能完整性和数据安全性的同时，也带来了一定的复杂性，需要在实际使用中根据业务需求和性能要求进行适当调整。