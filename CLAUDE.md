# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于Spring Cloud微服务架构的Java企业级应用，主要面向教育、保险、优惠券等业务场景。项目采用Maven多模块管理，包含10个主要微服务模块。

## 技术栈

- **Spring Boot**: 2.7.18
- **Spring Cloud**: 2021.0.8 
- **Spring Cloud Alibaba**: 2021.0.6.1
- **Java版本**: 1.8
- **服务注册发现**: Nacos
- **数据库**: MySQL + MyBatis Plus
- **消息队列**: RocketMQ
- **缓存**: Redis
- **任务调度**: XXL-Job
- **容器化**: Docker + Kubernetes

## 项目结构

### 基础服务模块
- `gateway` (端口:9999) - API网关服务
- `auth` - OAuth2认证服务  
- `upms` - 用户权限管理系统

### 业务应用模块
- `app-partner` (端口:6011) - 合作伙伴管理（代理商、学生、佣金结算）
- `app-insurance` - 保险业务（订单处理、自动投保）
- `app-coupon` - 优惠券管理
- `app-payment` - 支付服务
- `app-pmp` - PMP管理
- `app-job` - 定时任务服务
- `apps` - 应用管理

### 模块内部结构
每个业务模块采用标准分层架构：
- `*-api` - 定义DTO、VO、Entity、常量、枚举等
- `*-biz` - 业务逻辑实现、Controller、Service、Mapper等

## 常用开发命令

### 构建和打包
```bash
# 完整构建所有模块
mvn clean package -Dmaven.test.skip=true

# 构建特定模块
mvn clean package -Dmaven.test.skip=true -pl app-partner/app-partner-biz

# 构建指定模块及其依赖
mvn clean package -Dmaven.test.skip=true -pl app-partner/app-partner-biz -am
```

### Docker构建
```bash
# 构建Docker镜像（在模块根目录）
docker build -t app-name:tag .

# 查看现有镜像
docker images | grep hti
```

### 本地运行
```bash
# 启动单个服务（需要先启动依赖服务如Nacos、Redis、MySQL）
cd app-partner/app-partner-biz
mvn spring-boot:run

# 或使用java -jar方式
java -jar target/app-partner-biz-3.8.1.jar
```

### 环境配置
```bash
# 设置环境变量（开发环境）
export NACOS_HOST=************
export NACOS_PORT=8852

# 查看应用健康状态
curl http://localhost:端口/actuator/health
```

## 开发环境依赖

### 必需服务
1. **Nacos** (************:8852) - 服务注册发现和配置中心
2. **MySQL** - 主数据库
3. **Redis** - 缓存服务
4. **RocketMQ** - 消息队列

### 启动顺序
1. 基础中间件（Nacos、MySQL、Redis、RocketMQ）
2. `gateway` - API网关
3. `auth` - 认证服务
4. `upms` - 用户权限管理
5. 其他业务模块

## 配置管理

### Nacos配置
- 配置中心地址：`************:8852`
- 配置文件命名规则：`application-{profile}.yml` 和 `{service-name}-{profile}.yml`
- 支持配置加密（Jasypt）

### 环境配置
- `dev` - 开发环境
- `test` - 测试环境  
- `prod` - 生产环境

## 重要架构特性

### 微服务通信
- **同步调用**: OpenFeign + 服务发现
- **异步处理**: RocketMQ消息队列
- **网关路由**: Spring Cloud Gateway统一入口

### 数据访问
- **ORM**: MyBatis Plus
- **数据源**: HikariCP连接池
- **事务管理**: Spring事务管理

### 安全机制
- **认证**: OAuth2 + JWT
- **授权**: RBAC权限模型
- **资源保护**: @EnableResourceServer

### 监控运维
- **健康检查**: Spring Boot Actuator
- **应用监控**: Spring Boot Admin
- **日志管理**: Logback + 分环境配置

## 业务领域知识

### Partner模块核心概念
- **代理商体系**: 多级代理商管理
- **学生管理**: 学生信息、申请记录、Offer管理
- **佣金结算**: 自动计算和分期结算
- **合同管理**: 电子合同签署和管理

### Insurance模块核心概念  
- **策略模式**: 支持多保险公司（Allian、NIB等）
- **自动投保**: 异步处理投保流程
- **订单状态**: PENDING -> PROGRESSING -> SUCCESS/FAIL
- **信用卡加密**: AES加密保护敏感信息

### Coupon模块核心概念
- **优惠券类型**: 不同类型优惠券管理
- **发放策略**: 批量发放和个人发放
- **核销流程**: 短信验证码核销

## CI/CD流程

### Jenkins Pipeline
- **构建环境**: Kubernetes + Jenkins Agent
- **构建工具**: Maven + Docker
- **镜像仓库**: Harbor (************)
- **部署目标**: Kubernetes集群

### 部署配置
- **基础镜像**: arthas-cn:latest（集成Arthas诊断工具）
- **JVM参数**: -Xms512m -Xmx512m
- **启动延迟**: 60秒（等待依赖服务就绪）

## 代码规范

### 新建Java文件作者
- 所有新生成的Java文件作者必须写成 `VON`
- 不允许修改原有文件的作者名

### 包命名规范
- 基础包：`com.{module}.{layer}`
- 实体类：`com.{module}.entity`
- DTO/VO：`com.{module}.dto` / `com.{module}.vo`
- 服务层：`com.{module}.service`
- 控制器：`com.{module}.controller`

### 常用注解
- `@RestController` - REST控制器
- `@Service` - 服务层
- `@Mapper` - MyBatis映射器
- `@EnableResourceServer` - 资源服务器
- `@EnableFeignClients` - Feign客户端

## 注意事项

1. **多模块依赖**: 修改api模块时注意影响的biz模块
2. **数据库事务**: 跨服务调用时注意分布式事务处理
3. **配置加密**: 敏感配置使用Jasypt加密
4. **异步处理**: 耗时操作使用RocketMQ异步处理
5. **容器内存**: 注意JVM内存配置与容器限制匹配
6. **服务依赖**: 确保依赖服务（Nacos、Redis等）先启动