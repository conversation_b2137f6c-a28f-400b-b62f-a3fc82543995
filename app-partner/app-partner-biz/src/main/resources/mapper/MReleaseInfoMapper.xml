<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MReleaseInfoMapper">

    <select id="searchReleaseInfo" parameterType="com.partner.dto.MReleaseInfoParamsDto" resultType="com.partner.vo.MReleaseInfoVo">
        SELECT
            id,
            fk_platform_id as fkPlatformId,
            fk_platform_code as fkPlatformCode,
            title,
            version_num as versionNum,
            status,
            CASE 
                WHEN status = 0 THEN '待发布'
                WHEN status = 1 THEN '已发布'
                WHEN status = 2 THEN '已撤回'
                ELSE '未知'
            END as statusDesc,
            release_time as releaseTime,
            DATE_FORMAT(release_time, '%Y-%m-%d %H:%i:%s') as releaseTimeFormatted,
            release_user as releaseUser,
            withdraw_time as withdrawTime,
            DATE_FORMAT(withdraw_time, '%Y-%m-%d %H:%i:%s') as withdrawTimeFormatted,
            withdraw_user as withdrawUser,
            gmt_create as gmtCreate,
            DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') as gmtCreateFormatted,
            gmt_create_user as gmtCreateUser,
            gmt_modified as gmtModified,
            gmt_modified_user as gmtModifiedUser
        FROM m_release_info
        <where>
            <if test="params.fkPlatformId != null">
                AND fk_platform_id = #{params.fkPlatformId}
            </if>
            <if test="params.fkPlatformCode != null and params.fkPlatformCode != ''">
                AND fk_platform_code = #{params.fkPlatformCode}
            </if>
            <if test="params.title != null and params.title != ''">
                AND title LIKE CONCAT('%', #{params.title}, '%')
            </if>
            <if test="params.versionNum != null and params.versionNum != ''">
                AND version_num = #{params.versionNum}
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
            <if test="params.releaseUser != null and params.releaseUser != ''">
                AND release_user = #{params.releaseUser}
            </if>
        </where>
        <if test="params.orderBy != null and params.orderBy != ''">
            ORDER BY ${params.orderBy}
        </if>
    </select>

</mapper> 