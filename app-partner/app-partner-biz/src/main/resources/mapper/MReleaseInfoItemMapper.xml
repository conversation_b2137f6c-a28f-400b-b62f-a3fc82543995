<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MReleaseInfoItemMapper">

    <select id="searchReleaseInfoItem" parameterType="com.partner.dto.MReleaseInfoItemParamsDto" resultType="com.partner.vo.MReleaseInfoItemVo">
        SELECT
            item.id,
            item.fk_release_info_id as fkReleaseInfoId,
            item.title,
            item.description,
            item.permission_type as permissionType,
            CASE 
                WHEN item.permission_type = 0 THEN '全局'
                WHEN item.permission_type = 1 THEN '角色权限'
                ELSE '未知'
            END as permissionTypeDesc,
            item.fk_resource_keys as fkResourceKeys,
            item.gmt_create as gmtCreate,
            DATE_FORMAT(item.gmt_create, '%Y-%m-%d %H:%i:%s') as gmtCreateFormatted,
            item.gmt_create_user as gmtCreateUser,
            item.gmt_modified as gmtModified,
            DATE_FORMAT(item.gmt_modified, '%Y-%m-%d %H:%i:%s') as gmtModifiedFormatted,
            item.gmt_modified_user as gmtModifiedUser,
            info.title as releaseInfoTitle,
            info.version_num as releaseInfoVersionNum
        FROM m_release_info_item item
        LEFT JOIN m_release_info info ON item.fk_release_info_id = info.id
        <where>
            <if test="params.fkReleaseInfoId != null">
                AND item.fk_release_info_id = #{params.fkReleaseInfoId}
            </if>
            <if test="params.title != null and params.title != ''">
                AND item.title LIKE CONCAT('%', #{params.title}, '%')
            </if>
            <if test="params.permissionType != null">
                AND item.permission_type = #{params.permissionType}
            </if>
            <if test="params.fkResourceKey != null and params.fkResourceKey != ''">
                AND item.fk_resource_keys LIKE CONCAT('%', #{params.fkResourceKey}, '%')
            </if>
            <if test="params.gmtCreateUser != null and params.gmtCreateUser != ''">
                AND item.gmt_create_user = #{params.gmtCreateUser}
            </if>
        </where>
        <if test="params.orderBy != null and params.orderBy != ''">
            ORDER BY ${params.orderBy}
        </if>
    </select>

</mapper> 