package com.partner.controller;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.IoUtil;
import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.partner.enums.FileUploadEnum;
import com.partner.service.PartnerFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/1/13  18:46
 * @Version 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping("/partnerFile")
@Tag(description = "伙伴文件管理", name = "伙伴文件管理")
public class PartnerFileController {

    private final PartnerFileService partnerFileService;

    @Operation(summary = "文件上传-公有桶")
    @PostMapping("/upload")
    @Inner(value = false)
    public R upload(@RequestParam("file") @Valid @NotNull(message = "文件不能为空") MultipartFile file,
                    @RequestParam("code") @Valid @NotNull(message = "上传类型不能为空") String code,
                    String customerFileName) {
        return R.ok(partnerFileService.upload(file, false, code, customerFileName));
    }

    @Operation(summary = "文件上传-私有桶")
    @PostMapping("/uploadPrivate")
    @Inner(value = false)
    public R uploadPrivate(@RequestParam("file") @Valid @NotNull(message = "文件不能为空") MultipartFile file,
                           @RequestParam("code") @Valid @NotNull(message = "上传类型不能为空") String code,
                           String customerFileName) {
        return R.ok(partnerFileService.upload(file, true, code, customerFileName));
    }

    @Operation(summary = "下载文件-公有桶")
    @GetMapping("/download")
    @Inner(value = false)
    public void download(@RequestParam("fileKey") String fileKey, HttpServletResponse response) {
        partnerFileService.getFile("", fileKey, response, false);
    }

    @Operation(summary = "下载文件-私有桶")
    @GetMapping("/downloadPrivate")
    @Inner(value = false)
    public void downloadPrivate(@RequestParam("fileKey") String fileKey, HttpServletResponse response) {
        partnerFileService.getFile("", fileKey, response, true);
    }

    @Operation(summary = "下载文件-获取文件流-公有桶")
    @GetMapping("/getFileBytes")
    @Inner(value = false)
    public R<byte[]> getFileBytes(@RequestParam("fileKey") String fileKey) {
        return getFileBytesInternal(fileKey, false);
    }

    @Operation(summary = "下载文件-获取文件流-私密桶")
    @GetMapping("/getPrivateFileBytes")
    @Inner(value = false)
    public R<byte[]> getPrivateFileBytes(@RequestParam("fileKey") String fileKey) {
        return getFileBytesInternal(fileKey, true);
    }

    @Operation(summary = "下载文件-获取文件Base64-公有桶")
    @GetMapping("/getFileBase64")
    @Inner(value = false)
    public R<String> getFileBase64(@RequestParam("fileKey") String fileKey) {
        return getFileBase64Internal(fileKey, false);
    }

    @Operation(summary = "下载文件-获取文件Base64-私有桶")
    @GetMapping("/getPrivateFileBase64")
    @Inner(value = false)
    public R<String> getPrivateFileBase64(@RequestParam("fileKey") String fileKey) {
        return getFileBase64Internal(fileKey, true);
    }

    @GetMapping("/fileUploadEnum")
    @Operation(summary = "文件上传code枚举列表")
    @Inner(value = false)
    public List<FileUploadEnumVO> listAllFileUploadEnums() {
        return Arrays.stream(FileUploadEnum.values())
                .map(e -> new FileUploadEnumVO(
                        e.getCode(),
                        e.getMsg(),
                        e.getPlatform(),
                        e.getFileCenter(),
                        e.getMediaTable(),
                        e.getIsPrivate(),
                        e.getUploadTogether()
                ))
                .collect(Collectors.toList());
    }

    @Getter
    @AllArgsConstructor
    static class FileUploadEnumVO {
        private String code;
        private String msg;
        private String platform;
        private String fileCenter;
        private String mediaTable;
        private Boolean isPrivate;
        private Boolean uploadTogether;
    }

    // ========= 抽取的私有共用方法 =========

    private R<byte[]> getFileBytesInternal(String fileKey, boolean isPrivate) {
        try {
            InputStream inputStream = partnerFileService.getFileStream("", fileKey, isPrivate);
            return R.ok(IoUtil.readBytes(inputStream));
        } catch (Exception e) {
            return R.failed("获取文件流失败");
        }
    }

    private R<String> getFileBase64Internal(String fileKey, boolean isPrivate) {
        try {
            InputStream inputStream = partnerFileService.getFileStream("", fileKey, isPrivate);
            byte[] bytes = IoUtil.readBytes(inputStream);
            if (bytes == null || bytes.length == 0) {
                return R.failed("文件不存在或为空");
            }

            String fileType;
            try (InputStream is = new ByteArrayInputStream(bytes)) {
                fileType = FileTypeUtil.getType(is);
            } catch (IOException e) {
                fileType = null;
            }

            String mimeType = mapFileTypeToMime(fileType);
            String base64 = Base64.getEncoder().encodeToString(bytes);
            String result = "data:" + mimeType + ";base64," + base64;
            return R.ok(result);
        } catch (Exception e) {
            return R.failed("获取文件失败");
        }
    }

    private String mapFileTypeToMime(String fileType) {
        if (fileType == null) return "application/octet-stream";
        switch (fileType.toLowerCase()) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "mp4":
                return "video/mp4";
            case "mp3":
                return "audio/mpeg";
            case "txt":
                return "text/plain";
            case "zip":
                return "application/zip";
            case "rar":
                return "application/x-rar-compressed";
            default:
                return "application/octet-stream";
        }
    }
}
