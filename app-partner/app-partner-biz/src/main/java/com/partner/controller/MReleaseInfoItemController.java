package com.partner.controller;

import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.service.MReleaseInfoItemService;
import com.partner.vo.MReleaseInfoItemVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 *  控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/mReleaseInfoItem")
@Tag(description = "发版信息子项管理", name = "发版信息子项管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class MReleaseInfoItemController {

    private final MReleaseInfoItemService mReleaseInfoItemService;

    /**
     * 根据发版信息ID获取子项列表（带权限过滤）
     *
     * @return 过滤后的发版信息子项列表
     */
    @GetMapping("/findByReleaseInfoId/{releaseInfoId}")
    @Operation(summary = "根据发版信息ID获取子项列表", 
               description = "根据权限类型进行过滤：全局权限直接返回，角色权限需验证用户权限")
    @SysLog(value = "根据发版信息ID获取子项列表")
    public R<List<MReleaseInfoItemVo>> findByReleaseInfoId(@PathVariable Long releaseInfoId) {
        List<MReleaseInfoItemVo> result = mReleaseInfoItemService.findByReleaseInfoIdWithPermission(releaseInfoId);
        return R.ok(result);
    }

} 