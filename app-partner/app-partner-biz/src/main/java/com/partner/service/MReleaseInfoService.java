package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.MReleaseInfoParamsDto;
import com.partner.entity.MReleaseInfoEntity;
import com.partner.vo.MReleaseInfoVo;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface MReleaseInfoService extends IService<MReleaseInfoEntity> {

    /**
     * 获取已发布的发版信息列表
     *
     * @return 已发布的发版信息列表
     */
    List<MReleaseInfoVo> findReleasedList();

}
 