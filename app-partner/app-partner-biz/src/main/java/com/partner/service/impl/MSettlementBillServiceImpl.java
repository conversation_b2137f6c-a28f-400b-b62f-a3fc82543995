package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.finance.MSettlementBillParamsDto;
import com.partner.dto.finance.paramsmapper.MSettlementBillSearchParams;
import com.partner.entity.MAgentContractAccountEntity;
import com.partner.entity.MSettlementBillEntity;
import com.partner.enums.PartnerErrorEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.MAgentContractAccountMapper;
import com.partner.mapper.MSettlementBillMapper;
import com.partner.service.MSettlementBillService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.finance.MSettlementBillItemVo;
import com.partner.vo.finance.MSettlementBillVo;
import com.partner.vo.finance.MSettlementReportDetail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
@AllArgsConstructor
public class MSettlementBillServiceImpl  extends ServiceImpl<MSettlementBillMapper,MSettlementBillEntity> implements MSettlementBillService {

    private final MSettlementBillMapper mSettlementBillMapper;

    private final ResourceLoader resourceLoader;

    private final MAgentContractAccountMapper magentContractAccountMapper;

    @Override
    public IPage getMSettlementBillPage(Page page, MSettlementBillParamsDto dto) {
        MSettlementBillSearchParams searchParams = BeanCopyUtils.objClone(dto,MSettlementBillSearchParams::new);

        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        Long agentId=userinfo.getAgentId();

        searchParams.setAgentId(agentId);

        IPage<MSettlementBillVo> result = mSettlementBillMapper.getMSettlementBillPage(page, searchParams);
        result.setTotal(result.getTotal());

        return result;
    }

    @Override
    public List<MSettlementBillItemVo>  getMSettlementBillDetail(Long msettlementId) {
        List<MSettlementBillItemVo> resultvo= mSettlementBillMapper.getMSettlementBillDetail(msettlementId);
        return resultvo;
    }

    @Override
    public void downloadSettlementBill(MSettlementBillParamsDto dto, HttpServletResponse response) {
        try{
            response.setCharacterEncoding("UTF-8");
            BufferedOutputStream outputStream = new BufferedOutputStream(response.getOutputStream());

            String filename="template/SettlementBillTemplate.docx";
            try{
                MSettlementBillEntity settlementbillentity=mSettlementBillMapper.selectById(dto.getMsettlementId());
                if(ObjectUtil.isEmpty(settlementbillentity)){
                    throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":账单不存在!");
                }
                List<MSettlementReportDetail> listMSettlement=new ArrayList<MSettlementReportDetail>();
                listMSettlement=mSettlementBillMapper.getMSettlementReportDetail(dto);


                Map<String,List<MSettlementReportDetail>> currencyList= listMSettlement.stream().collect(Collectors.groupingBy(MSettlementReportDetail::getFkCurrencyTypeNum));

                //查询对账单签名
                String base64Image=mSettlementBillMapper.getBase64Image(dto);
                InputStream fis = getClass().getClassLoader().getResourceAsStream(filename);
                XWPFDocument document = new XWPFDocument(fis);

                XWPFTable table =document.getTables().get(0);
                if (ObjectUtil.isNotNull(table)) {
                    // 添加表格信息 第一个表格
                    addDynamicRowsToTable_add(table,listMSettlement);
                }
                XWPFTable table1 =document.getTables().get(1);
                if (ObjectUtil.isNotNull(table1)) {
                    // 添加表格信息 第二个表格
                    addDynamicRowsToTable_add1(table1,currencyList);
                }

                XWPFTable table2 =document.getTables().get(2);
                if (ObjectUtil.isNotNull(table2)) {
                    MAgentContractAccountEntity mAgentContractAccountEntity=magentContractAccountMapper.selectById(settlementbillentity.getFkAgentContractAccountId());
                    if(ObjectUtil.isNotEmpty(mAgentContractAccountEntity) ){
                        addDynamicRowsToTable_add2(table2,mAgentContractAccountEntity);
                    }
                }



                XWPFTable table3 =document.getTables().get(3);
                if (ObjectUtil.isNotNull(table3)) {
                    // 添加图片
                    processTable(table3,base64Image);
                }
                // 处理下载到的流
                // 这里是直接读取，按实际情况来处理
                byte[] bytes = null;
                try {
                    bytes = convertToByteArray(document);
                    outputStream.write(bytes);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public static byte[] convertToByteArray(XWPFDocument document) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 将文档写入 ByteArrayOutputStream
            document.write(baos);

            // 获取字节数组
            return baos.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            // 确保关闭 XWPFDocument 以释放资源
            if (document != null) {
                try {
                    document.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private  void processTable(XWPFTable table,String base64Image) {

        // 遍历每一行
        for (XWPFTableRow row : table.getRows()) {
            // 遍历每个单元格
            for (XWPFTableCell cell : row.getTableCells()) {
                // 遍历单元格中的段落
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    for (XWPFRun run : paragraph.getRuns()) {
                        String text = run.getText(0);
                        if (text != null && text.contains("${signature}")) {
                            // 插入签字图片
                            try{
                                // 解码 Base64 字符串，去掉 data URL 前缀（如果有）
                                byte[] imageBytes = Base64.getDecoder().decode(base64Image.split(",")[1]);
                                ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
                                run.addPicture(bis, Document.PICTURE_TYPE_PNG, "signature.png", Units.toEMU(200), Units.toEMU(100));
                                run.setText("", 0); // 清除原有文本
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            } catch (InvalidFormatException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                }
            }
        }



    }

    private  void addDynamicRowsToTable_add(XWPFTable table,List<MSettlementReportDetail> listData ) {
        // 获取表格的列数
        int columnCount = table.getRow(2).getTableCells().size(); // 第2行定义了表格结构
        // 动态添加多行数据，从第三行开始（索引为2）
        int startIndex = 2; // 第三行的索引
        int numNewRows = listData.size(); // 要添加的新行数
        XWPFTableRow firstRow = table.getRow(2);
        // 创建新行并设置内容
        for (int i = 0; i < numNewRows; i++) {
            MSettlementReportDetail detailInfo=listData.get(i);
            XWPFTableRow newRow = firstRow;
            for (int col = 0; col < columnCount; col++) {
                if(col==0){
                    setCellText(newRow.getCell(col), detailInfo.getAreaCountryNameChn());
                } else if (col==1) {
                    setCellText(newRow.getCell(col), detailInfo.getInstitutionNameChn());
                }else if (col==2) {
                    setCellText(newRow.getCell(col), detailInfo.getStudentsName());
                }else if (col==3) {
                    setCellText(newRow.getCell(col), detailInfo.getCourseName());
                }else if (col==4) {
                    if(ObjectUtil.isNotEmpty(detailInfo.getOpeningTime())){
                        setCellText(newRow.getCell(col), detailInfo.getOpeningTime().toString());
                    }
                }else if (col==5) {
                    if(ObjectUtil.isNotEmpty(detailInfo.getTuitionAmount())){
                        setCellText(newRow.getCell(col), detailInfo.getTuitionAmount().toString());
                    }
                }else if (col==6) {
                    setCellText(newRow.getCell(col), detailInfo.getFkCurrencyTypeNum());
                }else if (col==7) {
                    setCellText(newRow.getCell(col), detailInfo.getScale());
                }else if (col==8) {
                    if(ObjectUtil.isNotEmpty(detailInfo.getAmountActual())){
                        setCellText(newRow.getCell(col), detailInfo.getAmountActual().toString());
                    }
                }


            }
            table.addRow(newRow, startIndex + i);
        }

        table.removeRow(startIndex+numNewRows);

    }

    private  void addDynamicRowsToTable_add1(XWPFTable table,Map<String,List<MSettlementReportDetail>> currencyList ) {
        if(ObjectUtil.isNotEmpty(currencyList)){
            Set<String> setkeyCurrency= currencyList.keySet();
            int columnCount = table.getRow(1).getTableCells().size(); // 第2行定义了表格结构
            // 动态添加多行数据，从第二行开始（索引为1）
            int startIndex = 1; // 第二行的索引
            int numNewRows = setkeyCurrency.size(); // 要添加的新行数
            XWPFTableRow firstRow = table.getRow(startIndex);
            // 创建新行并设置内容
            for (String currency : setkeyCurrency) {
                int i=0;
                XWPFTableRow newRow = firstRow;
                for (int col = 0; col < columnCount; col++) {
                    if (col == 0) {
                        setCellText(newRow.getCell(col), currency);

                    } else if (col==1) {
                        List<MSettlementReportDetail> detail=currencyList.get(currency);
                        BigDecimal amountActual = detail.stream().map(MSettlementReportDetail::getAmountActual).reduce(BigDecimal.ZERO, BigDecimal::add);
                        setCellText(newRow.getCell(col), amountActual.toString());
                    }
                }

                table.addRow(newRow, startIndex + i);
                i++;
            }
            table.removeRow(startIndex+numNewRows);

        }

    }
    private  void addDynamicRowsToTable_add2(XWPFTable table,MAgentContractAccountEntity mAgentContractAccountEntity ) {

        // 遍历每一行
        for (XWPFTableRow row : table.getRows()) {
            // 遍历每个单元格
            for (XWPFTableCell cell : row.getTableCells()) {
                // 遍历单元格中的段落
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    for (XWPFRun run : paragraph.getRuns()) {
                        String text = run.getText(0);
                        if (text != null && text.contains("${Bank}")) {
                            run.setText(mAgentContractAccountEntity.getBankName(), 0);
                        } else if (text != null && text.contains("${Branch}")) {
                            run.setText(mAgentContractAccountEntity.getBankBranchName(),0);
                        }else if(text != null && text.contains("${AccountName}")){
                            run.setText(mAgentContractAccountEntity.getBankAccount(),0);
                        } else if (text != null && text.contains("${AccountNo}")) {
                            run.setText(mAgentContractAccountEntity.getBankAccountNum(),0);
                        } else if (text != null && text.contains("${BankAddress}")) {
                            run.setText(mAgentContractAccountEntity.getBankAddress(),0);
                        } else if (text != null && text.contains("${swiftCode}")) {
                            run.setText(mAgentContractAccountEntity.getAreaCountryCode(),0);
                        }
                    }
                }
            }
        }

    }

    private  void setCellText(XWPFTableCell cell, String text) {
        // 清除单元格内的所有段落
        while (cell.getParagraphs().size() > 0) {
            cell.removeParagraph(0);
        }
        // 创建新的段落并设置文本
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun run = paragraph.createRun();
        run.setText(text);
    }

}