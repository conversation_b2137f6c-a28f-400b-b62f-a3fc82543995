package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.MAgentContractAccountEntity;
import com.partner.enums.PartnerErrorEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.service.MAgentContractAccountService;
import com.partner.mapper.MAgentContractAccountMapper;
import com.partner.util.UserInfoParamsUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_agent_contract_account】的数据库操作Service实现
* @createDate 2025-01-09 14:06:45
*/
@Service
@AllArgsConstructor
public class MAgentContractAccountServiceImpl extends ServiceImpl<MAgentContractAccountMapper, MAgentContractAccountEntity>
    implements MAgentContractAccountService{

    private final MAgentContractAccountMapper agentContractAccountMapper;


    @Override
    public List<MAgentContractAccountEntity> getAgentContractAccount() {
        List<MAgentContractAccountEntity> result=new ArrayList<>();
        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());


        if(ObjectUtil.isEmpty(userinfo.getAgentId())){
            //UUID数据不存在!
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":代理不存在!");
        }
        result=agentContractAccountMapper.getAgentContractAccount(userinfo.getAgentId());

        return result;
    }
}




