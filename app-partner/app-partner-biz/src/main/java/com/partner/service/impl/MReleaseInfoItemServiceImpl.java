package com.partner.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.partner.constant.DataSourceConstant;
import com.partner.entity.MReleaseInfoItemEntity;
import com.partner.enums.ReleaseInfoItemPermissionTypeEnum;
import com.partner.mapper.MReleaseInfoItemMapper;
import com.partner.service.MReleaseInfoItemService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.MReleaseInfoItemVo;
import com.partner.vo.base.CurrentUserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DS(DataSourceConstant.PLATFORM)
public class MReleaseInfoItemServiceImpl extends ServiceImpl<MReleaseInfoItemMapper, MReleaseInfoItemEntity> implements MReleaseInfoItemService {

    private final MReleaseInfoItemMapper mReleaseInfoItemMapper;

    /**
     * 根据发版信息ID获取子项列表（带权限过滤）
     *
     * @param releaseInfoId 发版信息ID
     * @return 过滤后的发版信息子项列表
     */
    @Override
    public List<MReleaseInfoItemVo> findByReleaseInfoIdWithPermission(Long releaseInfoId) {
        // 1. 获取当前用户上下文信息
        CurrentUserContext userContext = UserInfoParamsUtils.getCurrentUserContextWithValidation();
        List<String> userPermissionKeys = userContext.getPermissionKeys();
        
        log.info("查询发版信息子项，发版信息ID：{}，当前用户ID：{}，用户权限数量：{}", 
                releaseInfoId, userContext.getUserId(), 
                CollectionUtils.isEmpty(userPermissionKeys) ? 0 : userPermissionKeys.size());

        // 2. 性能优化：将权限List转换为HashSet，提高查询效率 O(k) -> O(1)
        Set<String> userPermissionKeySet = CollectionUtils.isEmpty(userPermissionKeys) 
                ? Collections.emptySet() 
                : new HashSet<>(userPermissionKeys);

        // 3. 查询所有子项
        LambdaQueryWrapper<MReleaseInfoItemEntity> queryWrapper = new LambdaQueryWrapper<MReleaseInfoItemEntity>()
                .eq(MReleaseInfoItemEntity::getFkReleaseInfoId, releaseInfoId)
                .orderByAsc(MReleaseInfoItemEntity::getId);
        
        List<MReleaseInfoItemEntity> allItems = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(allItems)) {
            log.info("发版信息ID：{}，未找到任何子项数据", releaseInfoId);
            return Collections.emptyList();
        }

        // 4. 权限过滤（优化后复杂度：O(n × m)，优化前：O(n × m × k)）
        List<MReleaseInfoItemEntity> filteredItems = allItems.stream()
                .filter(item -> hasPermissionOptimized(item, userPermissionKeySet))
                .collect(Collectors.toList());

        log.info("发版信息ID：{}，总子项数：{}，过滤后子项数：{}", 
                releaseInfoId, allItems.size(), filteredItems.size());

        // 5. 转换为VO对象
        return filteredItems.stream()
                .map(item -> BeanCopyUtils.objClone(item, MReleaseInfoItemVo::new))
                .collect(Collectors.toList());
    }

    /**
     * 检查用户是否有权限查看该子项（性能优化版本）
     *
     * @param item 发版信息子项
     * @param userPermissionKeySet 用户权限Set集合（O(1)查找）
     * @return true-有权限，false-无权限
     */
    private boolean hasPermissionOptimized(MReleaseInfoItemEntity item, Set<String> userPermissionKeySet) {
        Integer permissionType = item.getPermissionType();
        
        // 全局权限，直接返回true
        if (ReleaseInfoItemPermissionTypeEnum.isGlobal(permissionType)) {
            return true;
        }
        
        // 角色权限，需要检查用户权限
        if (ReleaseInfoItemPermissionTypeEnum.needRoleCheck(permissionType)) {
            String fkResourceKeys = item.getFkResourceKeys();
            
            // 如果资源Keys为空，默认无权限
            if (!StringUtils.hasText(fkResourceKeys)) {
                log.debug("发版信息子项ID：{}，权限类型为角色权限但资源Keys为空", item.getId());
                return false;
            }
            
            // 如果用户权限为空，无权限
            if (userPermissionKeySet.isEmpty()) {
                log.debug("发版信息子项ID：{}，用户权限Set为空", item.getId());
                return false;
            }
            
            // 性能优化：使用Set.contains() O(1) 替代 List.contains() O(k)
            String[] requiredKeysArray = fkResourceKeys.split(",");
            boolean hasPermission = Arrays.stream(requiredKeysArray)
                    .anyMatch(key -> userPermissionKeySet.contains(key.trim()));
            
            if (log.isDebugEnabled()) {
                log.debug("发版信息子项ID：{}，所需权限：{}，权限检查结果：{}", 
                        item.getId(), Arrays.toString(requiredKeysArray), hasPermission);
            }
            
            return hasPermission;
        }
        
        // 未知权限类型，默认无权限
        log.warn("发版信息子项ID：{}，权限类型未知：{}", item.getId(), permissionType);
        return false;
    }

}
 