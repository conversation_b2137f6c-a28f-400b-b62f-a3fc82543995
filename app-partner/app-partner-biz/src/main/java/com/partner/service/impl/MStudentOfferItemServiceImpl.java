package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.offeritem.MAppStudentOfferItemDeleteDto;
import com.partner.dto.offeritem.MAppStudentOfferItemUpdateDto;
import com.partner.dto.student.MAppStudentAddApplyDto;
import com.partner.dto.student.MAppStudentOfferItemDto;
import com.partner.entity.MAppStudentEntity;
import com.partner.entity.MAppStudentOfferItemEntity;
import com.partner.entity.MStudentOfferItemEntity;
import com.partner.enums.PartnerErrorEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.MAppStudentMapper;
import com.partner.mapper.MAppStudentOfferItemMapper;
import com.partner.mapper.MStudentOfferItemMapper;
import com.partner.service.MStudentOfferItemService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.UserInfoParamsUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class MStudentOfferItemServiceImpl  extends ServiceImpl<MStudentOfferItemMapper, MStudentOfferItemEntity>
        implements MStudentOfferItemService {

    private final MAppStudentOfferItemMapper mAppStudentOfferItemMapper;
    private final MAppStudentMapper mAppStudentMapper;


    @Override
    public boolean addOfferItemStudents(MAppStudentAddApplyDto dto) {

        List<MAppStudentOfferItemDto> list=dto.getList();

        if(list==null || list.size()==0){
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,"申请计划不能为空！");
        }
        //校验只有partner来的学生可以进行加申
        MAppStudentEntity mAppStudentEntity= mAppStudentMapper.selectById(dto.getFkAppStudentId());
        if(ObjectUtil.isEmpty(mAppStudentEntity)){
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,"非partner学生不能加申！");
        }


        FzhUser user = SecurityUtils.getUser();
        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()),
                user.getFkFromPlatformCode(), user.getId());


        List<MAppStudentOfferItemEntity> offerItemAddList =new ArrayList<>();
        for(MAppStudentOfferItemDto tmp:list){
            MAppStudentOfferItemEntity offerItemTmp = new MAppStudentOfferItemEntity();

            BeanCopyUtils.copyProperties(tmp, offerItemTmp);
            offerItemTmp.setFkAppStudentId(dto.getFkAppStudentId());
            offerItemTmp.setIsAdditional(true);
            offerItemTmp.setStatusAdditional(1);
            offerItemTmp.setGmtCreate(LocalDateTime.now());
            offerItemTmp.setGmtCreateUser(userInfoParams.getPartnerUserId().toString());
            offerItemAddList.add(offerItemTmp);
        }
        if(ObjectUtil.isNotEmpty(offerItemAddList)){
            mAppStudentOfferItemMapper.insert(offerItemAddList);
        }
        return true;
    }

    @Override
    public boolean updateOfferItemStudents(MAppStudentOfferItemUpdateDto params) {
        List<Integer> statusArr=new ArrayList<>();
        statusArr.add(-1);
        statusArr.add(0);
        statusArr.add(1);

        MAppStudentOfferItemEntity offerItemEntity= mAppStudentOfferItemMapper.selectOne(new LambdaQueryWrapper<MAppStudentOfferItemEntity>()
                .eq(MAppStudentOfferItemEntity::getFkAppStudentId, params.getFkAppStudentId())
                .eq(MAppStudentOfferItemEntity::getId, params.getId())
                .eq(MAppStudentOfferItemEntity::getIsAdditional,true)
                        .in(MAppStudentOfferItemEntity::getStatusAdditional,statusArr)
                ,false
        );

        if(ObjectUtil.isEmpty(offerItemEntity)){
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,"申请计划不能修改！");
        }
        FzhUser user = SecurityUtils.getUser();
        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()),
                user.getFkFromPlatformCode(), user.getId());
        offerItemEntity.setFkAreaCountryId(params.getFkAreaCountryId());
        if(params.getFkInstitutionId()!=null){
            offerItemEntity.setFkInstitutionId(params.getFkInstitutionId());
        }else {
            offerItemEntity.setFkInstitutionId(null);
        }

        if(params.getFkInstitutionCourseId()!=null){
            offerItemEntity.setFkInstitutionCourseId(params.getFkInstitutionCourseId());
        }else {
            offerItemEntity.setFkInstitutionCourseId(null);
        }

        offerItemEntity.setOpeningTime(params.getOpeningTime());
        offerItemEntity.setGmtModified(LocalDateTime.now());
        offerItemEntity.setGmtModifiedUser(userInfoParams.getPartnerUserId().toString());
        mAppStudentOfferItemMapper.updateInfoById(offerItemEntity);
        return true;
    }

    @Override
    public boolean removeById(MAppStudentOfferItemDeleteDto params) {
        List<Integer> statusArr=new ArrayList<>();
        statusArr.add(-1);
        statusArr.add(0);

        MAppStudentOfferItemEntity offerItemEntity= mAppStudentOfferItemMapper.selectOne(new LambdaQueryWrapper<MAppStudentOfferItemEntity>()
                        .eq(MAppStudentOfferItemEntity::getFkAppStudentId, params.getFkAppStudentId())
                        .eq(MAppStudentOfferItemEntity::getId, params.getId())
                        .eq(MAppStudentOfferItemEntity::getIsAdditional,true)
                        .in(MAppStudentOfferItemEntity::getStatusAdditional,statusArr)
                ,false
        );

        if(ObjectUtil.isEmpty(offerItemEntity)){
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,"非草稿计划不能删除！");
        }
        mAppStudentOfferItemMapper.deleteById(offerItemEntity.getId());


        return true;
    }


}
