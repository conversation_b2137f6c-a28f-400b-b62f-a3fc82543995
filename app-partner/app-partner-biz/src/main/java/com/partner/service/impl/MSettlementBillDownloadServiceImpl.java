package com.partner.service.impl;

import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.MFilePartnerDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.finance.DownloadMSettlementBillParamsDto;
import com.partner.dto.finance.MSettlementBillParamsDto;
import com.partner.entity.MFilePartnerEntity;
import com.partner.enums.ConfigTypeEnum;
import com.partner.mapper.MSettlementBillMapper;
import com.partner.service.FileService;
import com.partner.service.ITencentCloudService;
import com.partner.service.MSettlementBillDownloadService;
import com.partner.service.MSettlementBillPdfService;
import com.partner.util.*;
import com.partner.vo.FileArray;
import com.partner.vo.finance.DownLoadMSettlementZipVo;
import com.qcloud.cos.model.COSObjectInputStream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
@AllArgsConstructor
@Slf4j
public class MSettlementBillDownloadServiceImpl implements MSettlementBillDownloadService {
    private final MSettlementBillMapper mSettlementBillMapper;

    private final ITencentCloudService iTencentCloudService;
    private final FileService fileService;
    private final TencentCloudUtils tencentCloudUtils;

    private final MSettlementBillPdfService mSettlementBillPdfService;

    @Override
    public void dowanloadMSettlementBill(HttpServletResponse response, DownloadMSettlementBillParamsDto params) {

        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        Long agentId=userinfo.getAgentId();

        params.setAgentId(agentId);
        //查询不存在对账单 结算单
        List<DownLoadMSettlementZipVo>  mSettlementNofileList= mSettlementBillMapper.searchMSettlementBillNoFile(params);
        if(ObjectUtils.isNotEmpty(mSettlementNofileList)){
            for(DownLoadMSettlementZipVo tmp:mSettlementNofileList){
                try{

                    MSettlementBillParamsDto paramsdto=new MSettlementBillParamsDto();
                    paramsdto.setMsettlementId(tmp.getId());
                    mSettlementBillPdfService.createSettlementPdf(paramsdto);
                }catch (Exception e){
                    log.error("批量生成对账单失败 {} {} {}", e.getMessage(), e,tmp.getId());
                }

            }
        }
        List<DownLoadMSettlementZipVo>  downLoadMSettlementList= mSettlementBillMapper.searchMSettlementBillDownload(params);


        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");

        BufferedOutputStream outputStream =null;

        if(ObjectUtils.isNotEmpty(downLoadMSettlementList)){
            List<MFilePartnerDto> downfileArr= new ArrayList<>();
            for(DownLoadMSettlementZipVo tmp:downLoadMSettlementList){
                MFilePartnerDto mfileparam=BeanCopyUtils.objClone(tmp,MFilePartnerDto::new);
                downfileArr.add(mfileparam);
            }

            List<COSObjectInputStream> cosArray= iTencentCloudService.downLoadPrivateCos(downfileArr);
            List<String> filenameList= downLoadMSettlementList.stream().map(o->o.getFileNameOrc()).collect(Collectors.toList());
            List<String> agentNameList= downLoadMSettlementList.stream().map(o->o.getAgentName()).collect(Collectors.toList());
            try{
                outputStream =new BufferedOutputStream(response.getOutputStream());
                ZipOutputStream zos = new ZipOutputStream(outputStream);

                for (int i = 0; i < cosArray.size(); i++) {
                    COSObjectInputStream cosStream = cosArray.get(i);
                    String fileName = filenameList.get(i);
                    String agentName = agentNameList.get(i);

                    // 创建 ZIP 条目（即 ZIP 里的一个文件）
                    ZipEntry zipEntry = new ZipEntry(agentName+fileName);
                    zos.putNextEntry(zipEntry);

                    // 将 COS 文件流写入 ZIP
                    IOUtils.copy(cosStream, zos);

                    // 关闭当前条目
                    zos.closeEntry();
                    //cosStream.close(); // 关闭 COS 流
                }
                zos.finish();
                //ZipFileUtil.packToZip(cosArray,filename,"E:\\tmpDownload\\对账单20250515.zip");
            }catch (Exception e){
                log.error(e.getMessage(),e);
            }finally {
                    try {
                        if (outputStream != null) {
                            outputStream.flush();
                            outputStream.close();
                        }
                    } catch (Exception e) {

                    }
            }


        }
    }

    @Override
    public FileArray dowanloadMSettlementBillCloud(DownloadMSettlementBillParamsDto params) {
        FileArray result=new FileArray();

        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        Long agentId=userinfo.getAgentId();

        params.setAgentId(agentId);
        //查询不存在对账单 结算单
        List<DownLoadMSettlementZipVo>  mSettlementNofileList= mSettlementBillMapper.searchMSettlementBillNoFile(params);
        if(ObjectUtils.isNotEmpty(mSettlementNofileList)){
            for(DownLoadMSettlementZipVo tmp:mSettlementNofileList){
                try{

                    MSettlementBillParamsDto paramsdto=new MSettlementBillParamsDto();
                    paramsdto.setMsettlementId(tmp.getId());
                    mSettlementBillPdfService.createSettlementPdf(paramsdto);
                }catch (Exception e){
                    log.error("批量生成对账单失败 ex={} {}", e.getMessage(), e);
                }

            }
        }




        List<DownLoadMSettlementZipVo>  downLoadMSettlementList= mSettlementBillMapper.searchMSettlementBillDownload(params);
        try {
            String filename="settlementBill"+new Date().getTime()+".zip";
            File file=new File(filename);
            FileOutputStream fileOutputStream = new FileOutputStream(filename);




            if(ObjectUtils.isNotEmpty(downLoadMSettlementList)){
                List<MFilePartnerDto> downfileArr= new ArrayList<>();
                for(DownLoadMSettlementZipVo tmp:downLoadMSettlementList){
                    MFilePartnerDto mfileparam=BeanCopyUtils.objClone(tmp,MFilePartnerDto::new);
                    downfileArr.add(mfileparam);
                }

                List<COSObjectInputStream> cosArray= iTencentCloudService.downLoadPrivateCos(downfileArr);
                List<String> filenameList= downLoadMSettlementList.stream().map(o->o.getFileNameOrc()).collect(Collectors.toList());
                List<String> agentNameList= downLoadMSettlementList.stream().map(o->o.getAgentName()).collect(Collectors.toList());
                try{

                    ZipOutputStream zos = new ZipOutputStream(fileOutputStream);

                    for (int i = 0; i < cosArray.size(); i++) {
                        COSObjectInputStream cosStream = cosArray.get(i);
                        String fileName = filenameList.get(i);
                        String agentName = agentNameList.get(i);

                        // 创建 ZIP 条目（即 ZIP 里的一个文件）
                        ZipEntry zipEntry = new ZipEntry(agentName+fileName);
                        zos.putNextEntry(zipEntry);

                        // 将 COS 文件流写入 ZIP
                        IOUtils.copy(cosStream, zos);

                        // 关闭当前条目
                        zos.closeEntry();

                    }
                    zos.finish();
                    MFilePartnerEntity filePartner=fileService.uploadFileAppendix(file,true);
                    if(ObjectUtils.isNotEmpty(filePartner)){
                        String baseurl=tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
                        result.setFileNameOrc(filePartner.getFileNameOrc());
                        result.setFileKey(baseurl+filePartner.getFileKey());
                    }
                }catch (Exception e){
                    log.error(e.getMessage(),e);
                }

            }

        }catch (Exception e){
            log.error("批量下载对账单失败 ex={} {}", e.getMessage(), e);
        }


        return result;
    }


}
