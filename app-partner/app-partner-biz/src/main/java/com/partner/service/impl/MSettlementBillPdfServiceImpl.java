package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.partner.dto.finance.MSettlementBillParamsDto;
import com.partner.entity.MAgentContractAccountEntity;
import com.partner.entity.MAgentEntity;
import com.partner.entity.MSettlementBillEntity;
import com.partner.enums.FileUploadEnum;
import com.partner.enums.PartnerErrorEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.MAgentContractAccountMapper;
import com.partner.mapper.MAgentMapper;
import com.partner.mapper.MSettlementBillMapper;
import com.partner.service.MSettlementBillPdfService;
import com.partner.service.PartnerFileService;
import com.partner.util.FileConvertUtil;
import com.partner.util.MyDateUtils;
import com.partner.vo.file.UploadFileParam;
import com.partner.vo.file.UploadFileVo;
import com.partner.vo.finance.MSettlementReportDetail;
import lombok.AllArgsConstructor;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;


@Service
@AllArgsConstructor
public class MSettlementBillPdfServiceImpl implements MSettlementBillPdfService {

    private static final Logger log = LoggerFactory.getLogger(MSettlementBillPdfServiceImpl.class);

    private final MSettlementBillMapper mSettlementBillMapper;
    private final MAgentContractAccountMapper magentContractAccountMapper;

//    private final FileService fileService;
//    private final SMediaAndAttachedMapper sMediaAndAttachedMapper;
    private final MAgentMapper mAgentMapper;
    private final PartnerFileService partnerFileService;

    @Override
    public void createSettlementPdf(MSettlementBillParamsDto dto) {

        // 创建一个Document对象
        Document document = new Document();
        MSettlementBillEntity settlementbillentity = mSettlementBillMapper.selectById(dto.getMsettlementId());
        if (ObjectUtil.isEmpty(settlementbillentity)) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":账单不存在!");
        }
        try {
            String filename = dto.getMsettlementId() + "_" + MyDateUtils.formatDate(LocalDate.now()) + ".pdf";
            File file = new File(filename);
            FileOutputStream fileOutputStream = new FileOutputStream(filename);
            BufferedOutputStream outputStream = new BufferedOutputStream(fileOutputStream);
            MAgentContractAccountEntity mAgentContractAccountEntity = magentContractAccountMapper.selectById(settlementbillentity.getFkAgentContractAccountId());
            String base64Svg = mSettlementBillMapper.getBase64Image(dto);
            MAgentEntity mAgentEntity = mAgentMapper.selectById(mAgentContractAccountEntity.getFkAgentId());

            List<MSettlementReportDetail> listMSettlement = new ArrayList<MSettlementReportDetail>();
            listMSettlement = mSettlementBillMapper.getMSettlementReportDetail(dto);
            Map<String, List<MSettlementReportDetail>> currencyList = listMSettlement.stream().collect(Collectors.groupingBy(MSettlementReportDetail::getFkCurrencyTypeNum));
            createPdfInfo(document, outputStream, listMSettlement, currencyList, settlementbillentity, dto, mAgentContractAccountEntity, base64Svg, mAgentEntity);

            /*if(outputStream!=null){
                outputStream.close();
            }
            if(fileOutputStream!=null){
                fileOutputStream.close();
            }*/
            // 关闭document
            if (document != null) {
                document.close();
            }

//            MFilePartnerEntity filePartner = fileService.uploadFileAppendix(file, false);
//
//            SMediaAndAttachedEntity sMediaAndAttachedEntity = new SMediaAndAttachedEntity();
//            sMediaAndAttachedEntity.setFkFileGuid(filePartner.getFileGuid());
//            sMediaAndAttachedEntity.setFkTableName("m_settlement_bill");
//            sMediaAndAttachedEntity.setFkTableId(dto.getMsettlementId());
//            sMediaAndAttachedEntity.setRemark("对账单信息");
//
//            sMediaAndAttachedMapper.insertSelective(sMediaAndAttachedEntity);

            //上传文件
            UploadFileVo uploadFileVo;
            try {
                uploadFileVo = partnerFileService.upload(FileConvertUtil.fileToMultipartFile(file), true, FileUploadEnum.APP_PARTNER_CENTER_SETTLEMENT_ATTACH.getCode(),filename);
                //保存到对应的媒体库和文件库
                UploadFileParam uploadFileParam = UploadFileParam.builder()
                        .fileDb(StringUtils.substringBefore(FileUploadEnum.APP_PARTNER_CENTER_SETTLEMENT_ATTACH.getFileCenter(), "."))
                        .fileTable(StringUtils.substringAfter(FileUploadEnum.APP_PARTNER_CENTER_SETTLEMENT_ATTACH.getFileCenter(), "."))
                        .mediaDb(StringUtils.substringBefore(FileUploadEnum.APP_PARTNER_CENTER_SETTLEMENT_ATTACH.getMediaTable(), "."))
                        .mediaTable(StringUtils.substringAfter(FileUploadEnum.APP_PARTNER_CENTER_SETTLEMENT_ATTACH.getMediaTable(), "."))
                        .tableId(dto.getMsettlementId())
                        .tableName("m_settlement_bill")
                        .typeKey("m_settlement_bill")
                        .uploadTogether(Boolean.TRUE)
                        .mediaInfo(uploadFileVo)
                        .build();
                partnerFileService.saveFileAndMedia(uploadFileParam,false);
            } catch (Exception e) {
                log.error("上传文件失败:{}", e.getMessage());
                throw new PartnerExceptionInfo(500, "文件上传失败");
            }

        } catch (Exception e) {
            log.error("结算单PDF生成失败 ex={} {}", dto.getMsettlementId(), e.getMessage(), e);
        } finally {
            log.info("结算单PDF生成成功 ex={}", dto.getMsettlementId());


        }


    }

    @Override
    public void downloadSettlementBillPdf(MSettlementBillParamsDto dto, HttpServletResponse response) {
        // 创建一个Document对象
        Document document = new Document();
        MSettlementBillEntity settlementbillentity = mSettlementBillMapper.selectById(dto.getMsettlementId());
        if (ObjectUtil.isEmpty(settlementbillentity)) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":账单不存在!");
        }
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        try {

            List<MSettlementReportDetail> listMSettlement = new ArrayList<MSettlementReportDetail>();
            listMSettlement = mSettlementBillMapper.getMSettlementReportDetail(dto);
            Map<String, List<MSettlementReportDetail>> currencyList = listMSettlement.stream().collect(Collectors.groupingBy(MSettlementReportDetail::getFkCurrencyTypeNum));
            MAgentContractAccountEntity mAgentContractAccountEntity = magentContractAccountMapper.selectById(settlementbillentity.getFkAgentContractAccountId());
            String base64Svg = mSettlementBillMapper.getBase64Image(dto);
            MAgentEntity mAgentEntity = mAgentMapper.selectById(mAgentContractAccountEntity.getFkAgentId());


            response.setCharacterEncoding("UTF-8");
            BufferedOutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            createPdfInfo(document, outputStream, listMSettlement, currencyList, settlementbillentity, dto, mAgentContractAccountEntity, base64Svg, mAgentEntity);
        } catch (Exception e) {
            log.error("结算单PDF下载失败 ex={} {}", dto.getMsettlementId(), e.getMessage(), e);
        } finally {
            log.info("结算单PDF下载成功 ex={}", dto.getMsettlementId());
            // 关闭document
            if (document != null) {
                document.close();
            }

        }


    }

    public void createPdfInfo(Document document, BufferedOutputStream outputStream, List<MSettlementReportDetail> listMSettlement,
                              Map<String, List<MSettlementReportDetail>> currencyList, MSettlementBillEntity settlementbillentity
            , MSettlementBillParamsDto dto, MAgentContractAccountEntity mAgentContractAccountEntity, String base64Svg, MAgentEntity mAgentEntity) {
        try {
            PdfWriter.getInstance(document, outputStream);


            // 打开document
            document.open();

            // 创建一个有9列的表格
            int col = 9;
            int colhight = 30;
            int fontTitleSize = 16;
            int fontColSize = 8;
            /*BaseColor titlecolor=new BaseColor(89, 116, 243);*/
            BaseColor titlecolor = new BaseColor(216, 231, 244);
            PdfPTable table = new PdfPTable(col);

            float[] columnWidths = {0.7f, 2.5f, 1f, 2.5f, 1f, 1f, 0.5f, 1f, 1.3f}; // 列的宽度比例
            table.setWidths(columnWidths);
            table.setWidthPercentage(100); // 表格宽度占页面100%

            float pageWidth = PageSize.A4.getWidth(); // 获取页面总宽度
            float marginLeft = document.leftMargin(); // 左边距
            float marginRight = document.rightMargin(); // 右边距
            float availableWidth = pageWidth - marginLeft - marginRight; // 可用宽度


            // 加载图片
            String titlefilename = "template/title.png";
            byte[] titleImage = getResourceAsBytes(titlefilename);
            Image image = Image.getInstance(titleImage);
            // 调整图片宽度为表格宽度
            //image.scaleToFit(availableWidth, 35); // 保持宽高比，宽度设置为表格宽度
            image.scaleAbsolute(availableWidth, 35);
            PdfPCell cellImapge = new PdfPCell(image);
            cellImapge.setHorizontalAlignment(PdfPCell.ALIGN_CENTER); // 水平居中
            cellImapge.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);   // 垂直居中
            cellImapge.setFixedHeight(36);
            cellImapge.setColspan(col);
            cellImapge.setBorder(PdfPCell.NO_BORDER); // 设置边框为无色

            table.addCell(cellImapge); // 将图片添加到单元格

            //添加标题
            String value = "代理名称：金顶置业（上海）有限公司(金顶置业)";
            if (ObjectUtil.isNotEmpty(mAgentEntity)) {
                value = mAgentEntity.getName();
            }
            table.addCell(addTitleOrColCell(col, value, null, colhight, fontTitleSize));
            // 添加表头
            table.addCell(addTableHeadCell("国家"));
            table.addCell(addTableHeadCell("学校"));
            table.addCell(addTableHeadCell("学生姓名"));
            table.addCell(addTableHeadCell("课程"));
            table.addCell(addTableHeadCell("开课时间"));
            table.addCell(addTableHeadCell("学费"));
            table.addCell(addTableHeadCell("币种"));
            table.addCell(addTableHeadCell("合作方学费佣金比例"));
            table.addCell(addTableHeadCell("本期结算金额"));


            // 添加数据行
            for (int row = 0; row < listMSettlement.size(); row++) {

                MSettlementReportDetail detailrow = listMSettlement.get(row);

                table.addCell(addTitleOrColCell(1, detailrow.getAreaCountryNameChn(), null, colhight, fontColSize));

                table.addCell(addTitleOrColCell(1, detailrow.getInstitutionNameChn(), null, colhight, fontColSize));

                table.addCell(addTitleOrColCell(1, detailrow.getStudentsName(), null, colhight, fontColSize));

                table.addCell(addTitleOrColCell(1, detailrow.getCourseName(), null, colhight, fontColSize));


                table.addCell(addTitleOrColCell(1, MyDateUtils.formatDate(detailrow.getOpeningTime()), null, colhight, fontColSize));
                if (detailrow.getTuitionAmount() != null) {

                    table.addCell(addTitleOrColCell(1, detailrow.getTuitionAmount().toString(), null, colhight, fontColSize));
                } else {
                    table.addCell("");
                }

                table.addCell(addTitleOrColCell(1, detailrow.getFkCurrencyTypeNum(), null, colhight, fontColSize));

                table.addCell(addTitleOrColCell(1, detailrow.getScale(), null, colhight, fontColSize));
                if (detailrow.getAmountActual() != null) {
                    table.addCell(addTitleOrColCell(1, detailrow.getAmountActual().toString(), null, colhight, fontColSize));
                } else {
                    table.addCell("");
                }


            }
            // 将表格添加到文档中
            document.add(table);
            Paragraph tmpcol = new Paragraph(" ");
            tmpcol.setLeading(8);
            document.add(tmpcol);

            PdfPTable table2 = new PdfPTable(col);
            table2.setWidths(columnWidths);
            table2.setWidthPercentage(100); // 表格宽度占页面100%
            String title2 = "本期结算佣金（汇率浮动，以实际结算金额及汇率为准）";
            table2.addCell(addTitleOrColCell(col, title2, titlecolor, colhight - 6, fontTitleSize));

            Set<String> setkeyCurrency = currencyList.keySet();
            String accountCurrencyTypeNum = "";//账户币种
            BigDecimal accountAllAmount = BigDecimal.ZERO;
            for (String currency : setkeyCurrency) {
                table2.addCell(addTitleOrColCell(1, currency, null, colhight - 6, fontColSize));
                List<MSettlementReportDetail> detail = currencyList.get(currency);
                BigDecimal amountActual = detail.stream().map(MSettlementReportDetail::getAmountActual).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal amountExchange = detail.stream().map(MSettlementReportDetail::getAmountExchange).reduce(BigDecimal.ZERO, BigDecimal::add);
                accountCurrencyTypeNum = detail.get(0).getAccountCurrencyTypeNum();
                accountAllAmount = accountAllAmount.add(amountExchange);

                String valueAmountActual = amountActual.toString();
                if (accountCurrencyTypeNum != null && "FCY".equals(accountCurrencyTypeNum)) {

                } else {
                    valueAmountActual = valueAmountActual + "（小计：" + amountExchange.toString() + "" + accountCurrencyTypeNum + "）";
                }


                table2.addCell(addTitleOrColCell(8, valueAmountActual, null, colhight - 6, fontColSize));
            }


            document.add(table2);
            document.add(tmpcol);


            PdfPTable table3 = new PdfPTable(col);
            int table3_fontColSize = 8;
            table3.setWidths(columnWidths);
            table3.setWidthPercentage(100); // 表格宽度占页面100%

            String title3 = "需要回复确认人民币结算 or 外币结算\n" +
                    "如果人民币结算，提供人民币账户信息（中文填写）\n" +
                    "如果外币结算，提供外币账户信息（英文填写）\n" +
                    "外币结算默认统一结算金额较大币种，如有币种要求，回复确认；如收款银行是汇丰银行，默认分币种划款。";
            table3.addCell(addTitleOrColCell(col, title3, null, 40, table3_fontColSize));
            table3.addCell(addTitleOrColCell(2, "Bank：", titlecolor, colhight - 6, table3_fontColSize));
            table3.addCell(addTitleOrColCell(7, mAgentContractAccountEntity.getBankName(), null, colhight - 6, table3_fontColSize));


            table3.addCell(addTitleOrColCell(2, "Branch：", titlecolor, colhight - 6, table3_fontColSize));
            table3.addCell(addTitleOrColCell(7, mAgentContractAccountEntity.getBankBranchName(), null, colhight - 6, table3_fontColSize));

            table3.addCell(addTitleOrColCell(2, "Account name：", titlecolor, colhight - 6, table3_fontColSize));
            table3.addCell(addTitleOrColCell(7, mAgentContractAccountEntity.getBankAccount(), null, colhight - 6, table3_fontColSize));

            table3.addCell(addTitleOrColCell(2, "Account No：", titlecolor, colhight - 6, table3_fontColSize));
            table3.addCell(addTitleOrColCell(7, mAgentContractAccountEntity.getBankAccountNum(), null, colhight - 6, table3_fontColSize));
            table3.addCell(addTitleOrColCell(2, "Bank Address：", titlecolor, colhight - 6, table3_fontColSize));
            table3.addCell(addTitleOrColCell(7, mAgentContractAccountEntity.getBankAddress(), null, colhight - 6, table3_fontColSize));

            table3.addCell(addTitleOrColCell(2, "SWIFT CODE(外币账户填写)：", titlecolor, colhight - 6, table3_fontColSize));
            table3.addCell(addTitleOrColCell(7, mAgentContractAccountEntity.getAreaCountryCode(), null, colhight - 6, table3_fontColSize));

            if (accountCurrencyTypeNum != null && "FCY".equals(accountCurrencyTypeNum)) {

            } else {
                table3.addCell(addTitleOrColCell(2, "结算金额总计：", titlecolor, colhight - 6, table3_fontColSize));
                String settmentValue = accountAllAmount + accountCurrencyTypeNum + "（汇率浮动，以实际结算金额及汇率为准）";
                table3.addCell(addTitleOrColCell(7, settmentValue, null, colhight - 6, table3_fontColSize));
            }


            document.add(table3);
            document.add(tmpcol);


            PdfPTable table4 = new PdfPTable(col);
            float table4_fontColSize = 8f;
            table4.setWidths(columnWidths);
            table4.setWidthPercentage(100); // 表格宽度占页面100%
            String title4 = "注:佣金结算前必须保证已生成完整版合作协议";
            table4.addCell(addTitleOrColCell(col, title4, titlecolor, colhight, table4_fontColSize));

            table4.addCell(addTitleOrColCell(col, "结算周期：双月中旬邮件核对确认，建议收到核对邮件后第一时间回复确认。收到回复确认邮件后的 10 个工作日完成汇款。", null, colhight, table4_fontColSize));
            table4.addCell(addTitleOrColCell(col, "人民币结算：支付渠道为易思汇，汇率按照划款当天香港中国银行十点的汇率，首先外币转成港币，在港币转成人民币，会涉及两个汇率。第三方每笔收取 100 人民币\n" +
                    "手续费。且协议甲方必须和收款账户保持一致。", null, colhight, table4_fontColSize));
            PdfPCell cellSignatureLeft = addTitleOrColCell(2, "签名：", null, colhight * 2, 16);
            cellSignatureLeft.setBorder(PdfPCell.LEFT | PdfPCell.TOP | PdfPCell.BOTTOM); // 只显示左、上、下边
            table4.addCell(cellSignatureLeft);


            if (base64Svg != null && !"".equals(base64Svg)) {
                byte[] imageBytes = convertSvgToPng(base64Svg);

                // 加载图片

                Image signatureImage = Image.getInstance(imageBytes); // 替换为你的图片路径
                // 调整图片宽度为表格宽度
                //signatureImage.scaleToFit(29.5f, 41.3f); // 保持宽高比，宽度设置为表格宽度

                PdfPCell cellSignatureImage = new PdfPCell(signatureImage);
                cellSignatureImage.setHorizontalAlignment(PdfPCell.ALIGN_LEFT); // 水平居中
                cellSignatureImage.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);   // 垂直居中
                cellSignatureImage.setFixedHeight(80);
                cellSignatureImage.setPaddingLeft(10);
                cellSignatureImage.setBorderWidth(0.1f);
                cellSignatureImage.setColspan(7);
                cellSignatureImage.setBorder(PdfPCell.RIGHT | PdfPCell.TOP | PdfPCell.BOTTOM); // 只显示右、上、下边

                table4.addCell(cellSignatureImage); // 将图片添加到单元格
            } else {
                PdfPCell cellSignatureImage = new PdfPCell();
                cellSignatureImage.setColspan(7);
                cellSignatureImage.setBorderWidth(0.1f);
                cellSignatureImage.setBorder(PdfPCell.RIGHT | PdfPCell.TOP | PdfPCell.BOTTOM); // 只显示右、上、下边
                table4.addCell(cellSignatureImage); // 将图片添加到单元格

            }
            document.add(table4);

        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }


    }

    public static byte[] convertSvgToPng(String base64Svg) throws Exception {
        // 解码 Base64 字符串
        byte[] svgBytes = Base64.getDecoder().decode(base64Svg.split(",")[1]);

        // 创建 TranscoderInput
        TranscoderInput input = new TranscoderInput(new ByteArrayInputStream(svgBytes));

        // 创建 PNG Transcoder
        PNGTranscoder transcoder = new PNGTranscoder();

        // 创建输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        TranscoderOutput output = new TranscoderOutput(outputStream);

        // 转换 SVG 为 PNG
        transcoder.transcode(input, output);

        // 获取 PNG 字节数组
        return outputStream.toByteArray();
    }

    public byte[] getResourceAsBytes(String path) throws IOException {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(path);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            if (inputStream == null) {
                throw new IOException("Resource not found: " + path);
            }

            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            return outputStream.toByteArray();
        }
    }

    public static PdfPCell addTitleOrColCell(int col, String value, BaseColor colors, int colhight, float fontSize) {

        try {
            // 创建一个单元格并设置背景颜色
            // 设置中文字体
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
            Font chineseFont = new Font(baseFont, fontSize, Font.NORMAL);

            if (!ObjectUtils.isEmpty(colors)) {
                chineseFont.setColor(new BaseColor(68, 84, 106));
            }


            PdfPCell cell = new PdfPCell(new Paragraph(value, chineseFont));
            cell.setHorizontalAlignment(PdfPCell.ALIGN_CENTER); // 水平居中
            cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);   // 垂直居中
            cell.setColspan(col);
            cell.setFixedHeight(colhight);
            cell.setBorderWidth(0.1f);
            if (!ObjectUtils.isEmpty(colors)) {

                cell.setBackgroundColor(colors);
            }
            return cell;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static PdfPCell addTableHeadCell(String value) {
        try {
            // 创建一个单元格并设置背景颜色
            // 设置中文字体
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
            Font chineseFont = new Font(baseFont, 8, Font.NORMAL);
            chineseFont.setColor(new BaseColor(68, 84, 106));

            PdfPCell cell = new PdfPCell(new Paragraph(value, chineseFont));
            /*BaseColor titlecolor=new BaseColor(173, 216, 230);*/
            /*BaseColor titlecolor=new BaseColor(89, 116, 243);*/

            BaseColor titlecolor = new BaseColor(216, 231, 244);
            cell.setBackgroundColor(titlecolor);
            cell.setBorderWidth(0.1f);


            cell.setHorizontalAlignment(PdfPCell.ALIGN_CENTER); // 水平居中
            cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);   // 垂直居中

            return cell;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
