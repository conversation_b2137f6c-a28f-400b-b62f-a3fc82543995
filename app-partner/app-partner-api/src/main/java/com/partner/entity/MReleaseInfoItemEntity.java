package com.partner.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("m_release_info_item")
@Schema(description = "")
@EqualsAndHashCode(callSuper = true)
public class MReleaseInfoItemEntity extends Model<MReleaseInfoItemEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * <p> Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:发版信息子项Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "发版信息子项Id")
    @JsonProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * <p> Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:发版信息Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "发版信息Id")
    @JsonProperty("fkReleaseInfoId")
    @TableField("fk_release_info_id")
    private Long fkReleaseInfoId;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(200) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:标题 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "标题")
    @JsonProperty("title")
    @TableField("title")
    private String title;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:text &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:描述（富文本） &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "描述（富文本）")
    @JsonProperty("description")
    @TableField("description")
    private String description;

    /**
     * <p> Java类型:Integer &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:int(10) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:权限类型：0全局/1角色权限 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "权限类型：0全局/1角色权限")
    @JsonProperty("permissionType")
    @TableField("permission_type")
    private Integer permissionType;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:text &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:系统资源Keys，逗号分隔 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "系统资源Keys，逗号分隔")
    @JsonProperty("fkResourceKeys")
    @TableField("fk_resource_keys")
    private String fkResourceKeys;

    /**
     * <p> Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:创建时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "创建时间")
    @JsonProperty("gmtCreate")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:创建用户(登录账号) &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "创建用户(登录账号)")
    @JsonProperty("gmtCreateUser")
    @TableField("gmt_create_user")
    private String gmtCreateUser;

    /**
     * <p> Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:修改时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "修改时间")
    @JsonProperty("gmtModified")
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:修改用户(登录账号) &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "修改用户(登录账号)")
    @JsonProperty("gmtModifiedUser")
    @TableField("gmt_modified_user")
    private String gmtModifiedUser;


} 