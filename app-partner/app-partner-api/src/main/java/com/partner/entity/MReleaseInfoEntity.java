package com.partner.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("m_release_info")
@Schema(description = "")
@EqualsAndHashCode(callSuper = true)
public class MReleaseInfoEntity extends Model<MReleaseInfoEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * <p> Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:发版信息Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "发版信息Id")
    @JsonProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * <p> Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:平台应用Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "平台应用Id")
    @JsonProperty("fkPlatformId")
    @TableField("fk_platform_id")
    private Long fkPlatformId;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(64) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:平台应用CODE &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "平台应用CODE")
    @JsonProperty("fkPlatformCode")
    @TableField("fk_platform_code")
    private String fkPlatformCode;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(200) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:标题 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "标题")
    @JsonProperty("title")
    @TableField("title")
    private String title;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:版本号 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "版本号")
    @JsonProperty("versionNum")
    @TableField("version_num")
    private String versionNum;

    /**
     * <p> Java类型:Integer &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:int(10) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:枚举：0待发布/1已发布/2已撤回 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "枚举：0待发布/1已发布/2已撤回")
    @JsonProperty("status")
    @TableField("status")
    private Integer status;

    /**
     * <p> Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:发布时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "发布时间")
    @JsonProperty("releaseTime")
    @TableField("release_time")
    private Date releaseTime;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:发布人 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "发布人")
    @JsonProperty("releaseUser")
    @TableField("release_user")
    private String releaseUser;

    /**
     * <p> Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:撤回时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "撤回时间")
    @JsonProperty("withdrawTime")
    @TableField("withdraw_time")
    private Date withdrawTime;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:撤回人 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "撤回人")
    @JsonProperty("withdrawUser")
    @TableField("withdraw_user")
    private String withdrawUser;

    /**
     * <p> Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:创建时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "创建时间")
    @JsonProperty("gmtCreate")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:创建用户(登录账号) &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "创建用户(登录账号)")
    @JsonProperty("gmtCreateUser")
    @TableField("gmt_create_user")
    private String gmtCreateUser;

    /**
     * <p> Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:修改时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "修改时间")
    @JsonProperty("gmtModified")
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:修改用户(登录账号) &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "修改用户(登录账号)")
    @JsonProperty("gmtModifiedUser")
    @TableField("gmt_modified_user")
    private String gmtModifiedUser;


} 