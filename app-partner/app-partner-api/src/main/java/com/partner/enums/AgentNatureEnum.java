package com.partner.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 代理商性质枚举
 *
 * <AUTHOR>
 * @Date 2025-01-28 15:36:00
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum AgentNatureEnum {

    /**
     * 公司
     */
    COMPANY("1", "公司"),

    /**
     * 个人
     */
    PERSON("2", "个人"),

    /**
     * 工作室
     */
    STUDIO("3", "工作室"),

    /**
     * 国际学校
     */
    SCHOOL("4", "国际学校"),

    /**
     * 其他
     */
    OTHER("5", "其他"),

    /**
     * 个人账户公司
     */
    PERSONAL_ACCOUNT_COMPANY("6", "个人账户公司");

    /**
     * 唯一标识码
     */
    private final String code;

    /**
     * 代理商性质的显示名称
     */
    private final String msg;

    /**
     * 代理商性质枚举类型映射表
     */
    private static final Map<String, AgentNatureEnum> AGENT_NATURE_MAP = new HashMap<>();

    static {
        for (AgentNatureEnum agentNatureEnum : AgentNatureEnum.values()) {
            AGENT_NATURE_MAP.put(agentNatureEnum.getCode(), agentNatureEnum);
        }
    }

    /**
     * 根据唯一标识获取对应的代理商性质枚举实例
     *
     * @param code 代理商性质编码
     * @return 对应的代理商性质枚举实例，如果找不到则返回null
     */
    public static AgentNatureEnum getAgentNatureByCode(String code) {
        return AGENT_NATURE_MAP.get(code);
    }

}