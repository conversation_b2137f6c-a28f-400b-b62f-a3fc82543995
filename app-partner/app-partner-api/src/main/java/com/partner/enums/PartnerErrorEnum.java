package com.partner.enums;

public enum PartnerErrorEnum {

    CHECK_EXCEPTION(10086, "校验通知信息"),
    FILEEMPTY_EXCEPTION(60001, "文件为空"),
    FILETYPE_EXCEPTION(60002, "文件类型不允许上传"),
    REGISTER_USER_ERROR(50001, "注册用户失败"),
    GET_CACHE_USER_ERROR(50002, "获取用户缓存信息失败"),
    FEIGN_SERVICE_ERROR(500, "服务调用失败"),
    USER_INFO_ERROR(50003, "用户信息有误"),
    RESET_USER_PASSWORD_ERROR(50004, "重置密码失败"),
    
    // 用户登录信息相关异常
    USER_LOGIN_INFO_NULL(50010, "获取登录用户信息失败，用户未登录或登录已过期"),
    USER_BASE_INFO_INCOMPLETE(50011, "用户基础信息不完整，缺少必要的用户ID、租户ID或平台代码"),
    USER_TENANT_ID_FORMAT_ERROR(50012, "租户ID格式错误，无法转换为有效数字"),
    USER_BUSINESS_INFO_NULL(50013, "获取用户业务权限信息失败，用户可能未分配代理权限"),
    
    // 发版信息相关异常  
    RELEASE_INFO_NOT_FOUND(51001, "未找到发版信息"),
    RELEASE_INFO_STATUS_INVALID(51002, "发版信息状态无效"),
    RELEASE_INFO_QUERY_PARAM_INVALID(51003, "发版信息查询参数无效")
    ;

    public int errorCode;
    public String errorMessage;

    PartnerErrorEnum(int errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }


}
