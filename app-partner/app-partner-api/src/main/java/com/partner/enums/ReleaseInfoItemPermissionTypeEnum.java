package com.partner.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 发版信息子项权限类型枚举
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Getter
@AllArgsConstructor
public enum ReleaseInfoItemPermissionTypeEnum {

    /**
     * 全局权限 - 所有用户都可以查看
     */
    GLOBAL(0, "全局"),

    /**
     * 角色权限 - 需要检查用户的角色权限
     */
    ROLE(1, "角色权限");

    /**
     * 权限类型编码
     */
    private final Integer code;

    /**
     * 权限类型描述
     */
    private final String description;

    /**
     * 静态映射，便于快速查找
     */
    private static final Map<Integer, ReleaseInfoItemPermissionTypeEnum> CODE_MAP = new HashMap<>();

    static {
        for (ReleaseInfoItemPermissionTypeEnum typeEnum : values()) {
            CODE_MAP.put(typeEnum.getCode(), typeEnum);
        }
    }

    /**
     * 根据编码获取枚举对象
     *
     * @param code 权限类型编码
     * @return 对应的枚举对象，未找到返回null
     */
    public static ReleaseInfoItemPermissionTypeEnum getByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    /**
     * 检查编码是否有效
     *
     * @param code 权限类型编码
     * @return true-有效，false-无效
     */
    public static boolean isValidCode(Integer code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 是否为全局权限
     *
     * @param code 权限类型编码
     * @return true-全局权限，false-非全局权限
     */
    public static boolean isGlobal(Integer code) {
        ReleaseInfoItemPermissionTypeEnum typeEnum = getByCode(code);
        return typeEnum == GLOBAL;
    }

    /**
     * 是否需要检查角色权限
     *
     * @param code 权限类型编码
     * @return true-需要检查角色权限，false-不需要
     */
    public static boolean needRoleCheck(Integer code) {
        ReleaseInfoItemPermissionTypeEnum typeEnum = getByCode(code);
        return typeEnum == ROLE;
    }

}