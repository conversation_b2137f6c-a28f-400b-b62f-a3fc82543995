package com.partner.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 发版信息状态枚举
 *
 * <AUTHOR>
 * @Date 2025-08-06
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum ReleaseInfoStatusEnum {

    /**
     * 待发布
     */
    PENDING(0, "待发布"),

    /**
     * 已发布
     */
    PUBLISHED(1, "已发布"),

    /**
     * 已撤回
     */
    WITHDRAWN(2, "已撤回");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String msg;

    /**
     * 发版信息状态映射表
     */
    private static final Map<Integer, ReleaseInfoStatusEnum> RELEASE_INFO_STATUS_MAP = new HashMap<>();

    static {
        for (ReleaseInfoStatusEnum releaseInfoStatusEnum : ReleaseInfoStatusEnum.values()) {
            RELEASE_INFO_STATUS_MAP.put(releaseInfoStatusEnum.getCode(), releaseInfoStatusEnum);
        }
    }

    /**
     * 根据状态码获取对应的发版信息状态枚举实例
     *
     * @param code 状态码
     * @return 对应的发版信息状态枚举实例，如果找不到则返回null
     */
    public static ReleaseInfoStatusEnum getReleaseInfoStatusByCode(Integer code) {
        return RELEASE_INFO_STATUS_MAP.get(code);
    }

    /**
     * 判断是否为已发布状态
     *
     * @param code 状态码
     * @return true表示已发布，false表示未发布
     */
    public static boolean isPublished(Integer code) {
        ReleaseInfoStatusEnum statusEnum = getReleaseInfoStatusByCode(code);
        return statusEnum == PUBLISHED;
    }

    /**
     * 判断是否可以进行发布操作
     *
     * @param code 状态码
     * @return true表示可以发布，false表示不可以发布
     */
    public static boolean canPublish(Integer code) {
        ReleaseInfoStatusEnum statusEnum = getReleaseInfoStatusByCode(code);
        return statusEnum == PENDING;
    }

    /**
     * 判断是否可以进行撤回操作
     *
     * @param code 状态码
     * @return true表示可以撤回，false表示不可以撤回
     */
    public static boolean canWithdraw(Integer code) {
        ReleaseInfoStatusEnum statusEnum = getReleaseInfoStatusByCode(code);
        return statusEnum == PUBLISHED;
    }

}