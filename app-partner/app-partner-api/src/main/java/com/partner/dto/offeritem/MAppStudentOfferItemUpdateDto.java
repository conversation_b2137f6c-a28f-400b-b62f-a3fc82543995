package com.partner.dto.offeritem;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
@Data
@Schema(description = "修改草稿箱子")
public class MAppStudentOfferItemUpdateDto {


    @Schema(description = "appOfferItemid")
    @NotNull(message = "草稿申请id不能为空!")
    private int id;

    @Schema(description = "申请草稿学生Id")
    @NotNull(message = "申请草稿学生Id不能空!")
    private Long fkAppStudentId;


    @Schema(description = "国家Id")
    @NotNull(message = "申请国家Id不能空!")
    private Long fkAreaCountryId;


    @Schema(description = "学校Id")
    private Long fkInstitutionId;

    @Schema(description = "课程Id")
    private Long fkInstitutionCourseId;


    @Schema(description = "开学时间")
    private LocalDate openingTime;
}
