package com.partner.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 发版信息查询参数
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@Schema(description = "发版信息查询参数")
public class MReleaseInfoParamsDto {

    @Schema(description = "平台应用Id")
    private Long fkPlatformId;

    @Schema(description = "平台应用CODE")
    private String fkPlatformCode;

    @Schema(description = "标题(模糊查询)")
    private String title;

    @Schema(description = "版本号")
    private String versionNum;

    @Schema(description = "状态(0待发布/1已发布/2已撤回)")
    private Integer status;

    @Schema(description = "发布人")
    private String releaseUser;

    @Schema(description = "排序字段(默认按创建时间倒序)")
    private String orderBy = "gmt_create DESC";

    @Schema(description = "页码", example = "1")
    private Long current = 1L;

    @Schema(description = "每页大小", example = "10")
    private Long size = 10L;
}