package com.partner.dto.student;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.partner.entity.MAppStudentEntity;
import com.partner.entity.MAppStudentOfferItemEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;


@Data
@Schema(description = "添加学生参数")
public class MStudentAddOrEditDto extends MAppStudentEntity {


    @Schema(description = "学生UUID")
    @NotBlank(message = "学生UUID为空", groups = {Update.class})
    private String studentUUID;



    @Schema(description = "学生姓名（中）")
    @NotBlank(message = "学生姓名不能为空", groups = {Add.class, Update.class})
    private String name;

    @Schema(description = "生日")
    @NotNull(message = "生日不能为空", groups = {Add.class, Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private LocalDate birthday;


    @Schema(description = "性别")
    @NotBlank(message = "性别不能为空", groups = {Add.class, Update.class})
    private String gender;


    private Integer saveType;


    @Schema(description = "申请计划")
    private List<MAppStudentOfferItemEntity> list;



    @Schema(description = "学生多附件")
    private String[] fileGuidArray;


    public interface Update {
    }

    public interface Add {
    }


}
