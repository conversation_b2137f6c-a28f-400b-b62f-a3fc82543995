package com.partner.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/1/16  11:25
 * @Version 1.0
 * 保存团队成员DTO
 */
@Data
public class SaveTeamMemberDto {

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "电子邮箱")
    @NotBlank(message = "电子邮箱不能为空")
    @Email(message = "电子邮箱格式不正确")
    private String email;

    @Schema(description = "角色ID")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @Schema(description = "上司Partner用户ID-添加角色为管理员时不用传")
    private Long superiorId;

    @Schema(description = "国家ID数组")
    @NotNull(message = "国家ID不能为空")
    private List<Long> countyIds;

    @Schema(description = "是否有查看佣金权限:0否/1是-仅针对文案和顾问角色")
    private Boolean isViewCommission;

}
