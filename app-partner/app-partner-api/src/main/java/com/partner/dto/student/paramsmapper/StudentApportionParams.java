package com.partner.dto.student.paramsmapper;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "分配学生传入mapper参数(和权限信息)")
public class StudentApportionParams extends UserInfoParams {

    @Schema(description = "代理UUID")
    private String agentUUID;


    @Schema(description = "角色ID")
    private Long roleId;
}
