---
description: 代码规范和开发指南
globs: 
alwaysApply: true

---

## 代码规范备忘录

### 重要原则

- 尊重原有代码风格，避免不必要的重构
- 在保证功能正确的前提下，遵循企业级开发标准
- 如果原代码写得很差，不要学习其不规范的风格
- 全程使用中文交流，保持沟通清晰

### 开发准则

- 开发新功能前必须研究现有相似功能的实现方式
- 保持代码风格的一致性
- 复用已有的工具类和通用方法
- 遵循项目既有的设计模式和架构决策

### 注解使用规范

- @ParameterObject 不允许使用这个注解

## Partner模块特殊规范

### 异常处理规范

#### 异常设计原则
- 异常枚举必须清楚描述具体错误场景，避免使用模糊的通用描述
- 如果原有异常不合适或不明显，直接新增具体的异常枚举，不要使用原有的模糊异常
- 异常信息应包含足够上下文，让开发者快速定位问题

#### 异常分类编码规则
- 用户认证相关: 5001x (USER_LOGIN_INFO_NULL, USER_BASE_INFO_INCOMPLETE等)
- 发版信息相关: 5101x (RELEASE_INFO_NOT_FOUND, RELEASE_INFO_STATUS_INVALID等)
- 学生管理相关: 5201x 
- 代理商管理相关: 5301x
- 佣金结算相关: 5401x

#### 异常使用要求
- 抛异常前必须记录详细的错误日志
- 避免使用USER_INFO_ERROR(50003, "用户信息有误")这类模糊异常
- 使用具体异常如USER_LOGIN_INFO_NULL(50010, "获取登录用户信息失败，用户未登录或登录已过期")

#### 工具类使用
- 统一使用UserInfoParamsUtils.getCurrentUserContextWithValidation()获取用户上下文
- 避免重复编写用户信息获取和校验代码

#### 异常构造方式
- 推荐使用: `new PartnerExceptionInfo(PartnerErrorEnum.USER_LOGIN_INFO_NULL)`
- 避免使用: `new PartnerExceptionInfo(PartnerErrorEnum.USER_LOGIN_INFO_NULL.errorCode, PartnerErrorEnum.USER_LOGIN_INFO_NULL.errorMessage)`